@echo off
echo ========================================
echo Android 15 + LSPosed 调试脚本
echo ========================================
echo.

echo 1. 检查设备连接...
adb devices
echo.

echo 2. 安装最新APK...
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    adb install -r "app\build\outputs\apk\debug\app-debug.apk"
    echo ✅ APK已安装
) else (
    echo ❌ APK文件不存在，请先构建项目
    pause
    exit /b 1
)
echo.

echo 3. 检查Android版本...
for /f "tokens=*" %%i in ('adb shell getprop ro.build.version.sdk') do set SDK_VERSION=%%i
echo Android SDK版本: %SDK_VERSION%
if %SDK_VERSION% GEQ 35 (
    echo ✅ Android 15+ 检测到
) else (
    echo ⚠️ 非Android 15系统
)
echo.

echo 4. 检查LSPosed状态...
adb shell "pm list packages | grep org.lsposed"
if %errorlevel% equ 0 (
    echo ✅ LSPosed已安装
) else (
    echo ❌ LSPosed未安装
)
echo.

echo 5. 检查模块是否在LSPosed中激活...
echo 请手动检查LSPosed管理器中是否：
echo - 模块已启用（绿色勾选）
echo - 勾选了 com.android.bluetooth
echo - 勾选了 system_server
echo - 勾选了 com.example.bluetoothscanner
echo.
pause

echo 6. 重启设备以应用Hook...
echo 是否重启设备? (Y/N)
set /p restart_choice=
if /i "%restart_choice%"=="Y" (
    adb reboot
    echo 设备重启中，请等待...
    timeout /t 30 >nul
    echo 等待设备重新连接...
    adb wait-for-device
    timeout /t 10 >nul
)
echo.

echo 7. 清理并创建日志文件...
adb shell "rm -f /storage/emulated/0/Download/bluetooth_hook.log"
adb shell "mkdir -p /storage/emulated/0/Download/"
adb shell "touch /storage/emulated/0/Download/bluetooth_hook.log"
adb shell "chmod 666 /storage/emulated/0/Download/bluetooth_hook.log"
echo ✅ 日志文件已准备
echo.

echo 8. 启动APP进行自检...
adb shell "am start -n com.example.bluetoothscanner/.MainActivity"
timeout /t 3 >nul
echo.

echo 9. 检查Hook启动日志...
echo 查找模块加载日志:
adb logcat -d | findstr "BluetoothHook" | tail -20
echo.

echo 10. 检查蓝牙进程...
adb shell "ps | grep bluetooth"
echo.

echo 11. 强制启动蓝牙服务...
adb shell "am start-service -a android.bluetooth.adapter.action.REQUEST_ENABLE"
timeout /t 2 >nul
echo.

echo 12. 等待5秒后检查Hook日志文件...
timeout /t 5 >nul
echo Hook日志文件内容:
adb shell "cat /storage/emulated/0/Download/bluetooth_hook.log"
echo.

echo ========================================
echo 🔧 调试检查清单：
echo.
echo ✅ 检查项目：
echo 1. APK是否成功安装？
echo 2. LSPosed中模块是否显示为"已启用"？
echo 3. 是否勾选了所有必要的进程？
echo 4. 设备是否已重启？
echo 5. 是否看到"模块加载"相关日志？
echo.
echo ❌ 如果仍然不工作：
echo 1. 检查LSPosed版本是否支持Android 15
echo 2. 尝试使用EdXposed或其他Xposed框架
echo 3. 检查SELinux是否为Permissive模式
echo 4. 查看详细的logcat日志
echo ========================================
echo.

echo 按任意键开始实时监控日志...
pause >nul

echo.
echo 🔍 实时监控所有相关日志:
echo ========================================
echo 同时监控文件日志和系统日志...
echo 按Ctrl+C停止监控
echo.

start /b adb shell "tail -f /storage/emulated/0/Download/bluetooth_hook.log"
adb logcat | findstr -i "bluetoothhook\|xposed\|lsposed"

{"logs": [{"outputFile": "com.example.bluetoothscanner.app-mergeDebugResources-27:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\43eeba27e46c0f9e8270c8e6a59600bf\\transformed\\core-1.9.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "100", "startColumns": "4", "startOffsets": "8141", "endColumns": "100", "endOffsets": "8237"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8c486cec99d4fccb7893779ad5d22534\\transformed\\material-1.9.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,465,543,619,703,795,878,979,1098,1175,1238,1329,1398,1465,1565,1628,1693,1754,1822,1884,1942,2056,2116,2177,2234,2307,2430,2511,2591,2739,2820,2901,2990,3043,3097,3163,3241,3321,3405,3477,3551,3624,3694,3785,3856,3946,4041,4115,4198,4291,4340,4409,4495,4580,4642,4706,4769,4878,4970,5067,5160,5217,5275", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "endColumns": "12,77,75,83,91,82,100,118,76,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,79,147,80,80,88,52,53,65,77,79,83,71,73,72,69,90,70,89,94,73,82,92,48,68,85,84,61,63,62,108,91,96,92,56,57,79", "endOffsets": "460,538,614,698,790,873,974,1093,1170,1233,1324,1393,1460,1560,1623,1688,1749,1817,1879,1937,2051,2111,2172,2229,2302,2425,2506,2586,2734,2815,2896,2985,3038,3092,3158,3236,3316,3400,3472,3546,3619,3689,3780,3851,3941,4036,4110,4193,4286,4335,4404,4490,4575,4637,4701,4764,4873,4965,5062,5155,5212,5270,5350"}, "to": {"startLines": "2,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3169,3247,3323,3407,3499,3582,3683,3802,3879,3942,4033,4102,4169,4269,4332,4397,4458,4526,4588,4646,4760,4820,4881,4938,5011,5134,5215,5295,5443,5524,5605,5694,5747,5801,5867,5945,6025,6109,6181,6255,6328,6398,6489,6560,6650,6745,6819,6902,6995,7044,7113,7199,7284,7346,7410,7473,7582,7674,7771,7864,7921,7979", "endLines": "9,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "endColumns": "12,77,75,83,91,82,100,118,76,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,79,147,80,80,88,52,53,65,77,79,83,71,73,72,69,90,70,89,94,73,82,92,48,68,85,84,61,63,62,108,91,96,92,56,57,79", "endOffsets": "510,3242,3318,3402,3494,3577,3678,3797,3874,3937,4028,4097,4164,4264,4327,4392,4453,4521,4583,4641,4755,4815,4876,4933,5006,5129,5210,5290,5438,5519,5600,5689,5742,5796,5862,5940,6020,6104,6176,6250,6323,6393,6484,6555,6645,6740,6814,6897,6990,7039,7108,7194,7279,7341,7405,7468,7577,7669,7766,7859,7916,7974,8054"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c82f0ccdbe9412677623492bdd8999d\\transformed\\appcompat-1.6.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "515,623,727,834,916,1017,1131,1211,1290,1381,1474,1566,1660,1760,1853,1948,2041,2132,2226,2305,2410,2508,2606,2714,2814,2917,3072,8059", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "618,722,829,911,1012,1126,1206,1285,1376,1469,1561,1655,1755,1848,1943,2036,2127,2221,2300,2405,2503,2601,2709,2809,2912,3067,3164,8136"}}]}]}
com.example.bluetoothscanner.app-constraintlayout-2.1.4-0 C:\Users\<USER>\.gradle\caches\transforms-3\0048526f799978d53c8838b69626f826\transformed\constraintlayout-2.1.4\res
com.example.bluetoothscanner.app-viewpager2-1.0.0-1 C:\Users\<USER>\.gradle\caches\transforms-3\17473142177f1aaac34c6335ae7e71fa\transformed\viewpager2-1.0.0\res
com.example.bluetoothscanner.app-lifecycle-viewmodel-2.5.1-2 C:\Users\<USER>\.gradle\caches\transforms-3\205605b76180136cc8f42c99fdbd78ad\transformed\lifecycle-viewmodel-2.5.1\res
com.example.bluetoothscanner.app-activity-1.6.0-3 C:\Users\<USER>\.gradle\caches\transforms-3\37c02d9dbf743df3639b5dd94d23bea1\transformed\activity-1.6.0\res
com.example.bluetoothscanner.app-appcompat-1.6.1-4 C:\Users\<USER>\.gradle\caches\transforms-3\3c82f0ccdbe9412677623492bdd8999d\transformed\appcompat-1.6.1\res
com.example.bluetoothscanner.app-coordinatorlayout-1.1.0-5 C:\Users\<USER>\.gradle\caches\transforms-3\41f5bc421223c690c51c8e734819e8da\transformed\coordinatorlayout-1.1.0\res
com.example.bluetoothscanner.app-core-1.9.0-6 C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\res
com.example.bluetoothscanner.app-fragment-1.3.6-7 C:\Users\<USER>\.gradle\caches\transforms-3\560ccd516cd6de1b6638828af3eebb71\transformed\fragment-1.3.6\res
com.example.bluetoothscanner.app-lifecycle-livedata-core-2.5.1-8 C:\Users\<USER>\.gradle\caches\transforms-3\603b40d55b283007ec925a6b3494c797\transformed\lifecycle-livedata-core-2.5.1\res
com.example.bluetoothscanner.app-transition-1.2.0-9 C:\Users\<USER>\.gradle\caches\transforms-3\61017ffc1d01ff413e7d898b997b69c2\transformed\transition-1.2.0\res
com.example.bluetoothscanner.app-recyclerview-1.1.0-10 C:\Users\<USER>\.gradle\caches\transforms-3\74257452692e5c2c34a27252223c312c\transformed\recyclerview-1.1.0\res
com.example.bluetoothscanner.app-lifecycle-process-2.4.1-11 C:\Users\<USER>\.gradle\caches\transforms-3\8af27606e6293b3b18c49e9b214e0d7e\transformed\lifecycle-process-2.4.1\res
com.example.bluetoothscanner.app-material-1.9.0-12 C:\Users\<USER>\.gradle\caches\transforms-3\8c486cec99d4fccb7893779ad5d22534\transformed\material-1.9.0\res
com.example.bluetoothscanner.app-appcompat-resources-1.6.1-13 C:\Users\<USER>\.gradle\caches\transforms-3\902bda61c99a6c8f4092b3028f6c7343\transformed\appcompat-resources-1.6.1\res
com.example.bluetoothscanner.app-emoji2-views-helper-1.2.0-14 C:\Users\<USER>\.gradle\caches\transforms-3\96e99f535981952eca70dcea7436f9a8\transformed\emoji2-views-helper-1.2.0\res
com.example.bluetoothscanner.app-startup-runtime-1.1.1-15 C:\Users\<USER>\.gradle\caches\transforms-3\b905560b2da4be86f30e02660b710b15\transformed\startup-runtime-1.1.1\res
com.example.bluetoothscanner.app-savedstate-1.2.0-16 C:\Users\<USER>\.gradle\caches\transforms-3\baa876de43fb379b2b6dab8fbed6b608\transformed\savedstate-1.2.0\res
com.example.bluetoothscanner.app-cardview-1.0.0-17 C:\Users\<USER>\.gradle\caches\transforms-3\bf7e18ef8301d1d7f6bc63773afcfb9c\transformed\cardview-1.0.0\res
com.example.bluetoothscanner.app-annotation-experimental-1.3.0-18 C:\Users\<USER>\.gradle\caches\transforms-3\c15864de81153c1f158ef9d18de9ad3a\transformed\annotation-experimental-1.3.0\res
com.example.bluetoothscanner.app-lifecycle-viewmodel-savedstate-2.5.1-19 C:\Users\<USER>\.gradle\caches\transforms-3\dd7cfcb5a58d5491bfcb53900100245d\transformed\lifecycle-viewmodel-savedstate-2.5.1\res
com.example.bluetoothscanner.app-drawerlayout-1.1.1-20 C:\Users\<USER>\.gradle\caches\transforms-3\e38ee089648600abdf2b1b34f81c65de\transformed\drawerlayout-1.1.1\res
com.example.bluetoothscanner.app-core-ktx-1.9.0-21 C:\Users\<USER>\.gradle\caches\transforms-3\e80bc102ae7b3ad78be9ca567c1ef740\transformed\core-ktx-1.9.0\res
com.example.bluetoothscanner.app-lifecycle-runtime-2.5.1-22 C:\Users\<USER>\.gradle\caches\transforms-3\f0e7d6d799d6dcb9d78b59a41c74ca7d\transformed\lifecycle-runtime-2.5.1\res
com.example.bluetoothscanner.app-emoji2-1.2.0-23 C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\res
com.example.bluetoothscanner.app-pngs-24 C:\xposedshell\app\build\generated\res\pngs\debug
com.example.bluetoothscanner.app-resValues-25 C:\xposedshell\app\build\generated\res\resValues\debug
com.example.bluetoothscanner.app-rs-26 C:\xposedshell\app\build\generated\res\rs\debug
com.example.bluetoothscanner.app-packageDebugResources-27 C:\xposedshell\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.bluetoothscanner.app-packageDebugResources-28 C:\xposedshell\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.bluetoothscanner.app-merged_res-29 C:\xposedshell\app\build\intermediates\merged_res\debug
com.example.bluetoothscanner.app-debug-30 C:\xposedshell\app\src\debug\res
com.example.bluetoothscanner.app-main-31 C:\xposedshell\app\src\main\res

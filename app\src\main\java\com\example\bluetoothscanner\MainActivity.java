package com.example.bluetoothscanner;

import android.bluetooth.BluetoothAdapter;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;

/**
 * 简化的主界面 - 显示当前配置和日志
 */
public class MainActivity extends AppCompatActivity {

    // SharedPreferences相关
    private static final String PREFS_NAME = "bluetooth_hook_config";
    private static final String KEY_TARGET_MAC = "target_mac";
    private static final String DEFAULT_MAC = "32:EB:17:06:38:EF";

    private SharedPreferences prefs;
    private String currentTargetMac;

    /**
     * 检查模块是否激活 - 会被Xposed Hook
     */
    public boolean isModuleActive() {
        return false; // 默认返回false，Hook后会返回true
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 初始化SharedPreferences
        prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
        currentTargetMac = prefs.getString(KEY_TARGET_MAC, DEFAULT_MAC);

        createSimpleUI();
    }

    private void createSimpleUI() {
        // 检查模块状态
        boolean moduleActive = isModuleActive();
        String statusIcon = moduleActive ? "✅" : "❌";
        String statusText = moduleActive ? "模块已激活" : "模块未激活";
        
        // 创建主布局
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setPadding(32, 32, 32, 32);
        
        // 创建信息文本
        TextView textView = new TextView(this);
        textView.setText("📋 蓝牙Hook模块\n\n" +
                statusIcon + " " + statusText + "\n\n" +
                "🎯 目标MAC: " + currentTargetMac + "\n" +
                "📶 RSSI范围: [-80, -20] dBm\n" +
                "🌐 出现命令: curl POST (device_appear=true)\n" +
                "🌐 消失命令: curl POST (device_disappear=true)\n\n" +
                "💡 点击设置按钮修改目标MAC地址\n\n" +
                "📱 模块在蓝牙进程中独立运行，无需打开APP\n" +
                "📁 日志: 主要在LSPosed日志中，点击查看日志按钮\n\n" +
                "🔧 使用步骤：\n" +
                "1. 设置目标MAC地址\n" +
                "2. 在LSPosed管理器中激活模块\n" +
                "3. 勾选 com.android.bluetooth 进程\n" +
                "4. 重启手机\n" +
                "5. 点击'🔍 扫描蓝牙'按钮开始扫描\n" +
                "6. 点击查看日志查看Hook状态\n\n" +
                (moduleActive ?
                    "🟢 模块正常工作中..." :
                    "🔴 请检查LSPosed配置并重启设备"));

        textView.setTextSize(14);
        textView.setLineSpacing(8, 1.2f);
        
        // 创建第一行按钮布局
        LinearLayout buttonLayout1 = new LinearLayout(this);
        buttonLayout1.setOrientation(LinearLayout.HORIZONTAL);
        LinearLayout.LayoutParams buttonLayoutParams1 = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        buttonLayoutParams1.setMargins(0, 32, 0, 8);
        buttonLayout1.setLayoutParams(buttonLayoutParams1);

        // 设置MAC按钮
        Button setMacButton = new Button(this);
        setMacButton.setText("⚙️ 设置MAC");
        setMacButton.setLayoutParams(new LinearLayout.LayoutParams(0,
                LinearLayout.LayoutParams.WRAP_CONTENT, 1));
        setMacButton.setOnClickListener(v -> showSetMacDialog());

        // 查看日志按钮
        Button viewLogButton = new Button(this);
        viewLogButton.setText("📋 查看日志");
        LinearLayout.LayoutParams viewLogParams = new LinearLayout.LayoutParams(0,
                LinearLayout.LayoutParams.WRAP_CONTENT, 1);
        viewLogParams.setMargins(8, 0, 0, 0);
        viewLogButton.setLayoutParams(viewLogParams);
        viewLogButton.setOnClickListener(v -> viewLog());

        // 创建第二行按钮布局
        LinearLayout buttonLayout2 = new LinearLayout(this);
        buttonLayout2.setOrientation(LinearLayout.HORIZONTAL);
        LinearLayout.LayoutParams buttonLayoutParams2 = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        buttonLayoutParams2.setMargins(0, 8, 0, 16);
        buttonLayout2.setLayoutParams(buttonLayoutParams2);

        // 蓝牙扫描按钮
        Button scanBluetoothButton = new Button(this);
        scanBluetoothButton.setText("🔍 扫描蓝牙");
        scanBluetoothButton.setLayoutParams(new LinearLayout.LayoutParams(0,
                LinearLayout.LayoutParams.WRAP_CONTENT, 1));
        scanBluetoothButton.setOnClickListener(v -> startBluetoothScan());

        // 重启Hook按钮
        Button restartHookButton = new Button(this);
        restartHookButton.setText("🔄 重启Hook");
        LinearLayout.LayoutParams restartParams = new LinearLayout.LayoutParams(0,
                LinearLayout.LayoutParams.WRAP_CONTENT, 1);
        restartParams.setMargins(8, 0, 0, 0);
        restartHookButton.setLayoutParams(restartParams);
        restartHookButton.setOnClickListener(v -> showRestartDialog());

        // 组装第一行布局
        buttonLayout1.addView(setMacButton);
        buttonLayout1.addView(viewLogButton);

        // 组装第二行布局
        buttonLayout2.addView(scanBluetoothButton);
        buttonLayout2.addView(restartHookButton);

        mainLayout.addView(textView);
        mainLayout.addView(buttonLayout1);
        mainLayout.addView(buttonLayout2);

        setContentView(mainLayout);
    }

    /**
     * 启动蓝牙扫描
     */
    private void startBluetoothScan() {
        try {
            BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();

            if (bluetoothAdapter == null) {
                Toast.makeText(this, "❌ 设备不支持蓝牙", Toast.LENGTH_SHORT).show();
                return;
            }

            if (!bluetoothAdapter.isEnabled()) {
                Toast.makeText(this, "⚠️ 请先开启蓝牙", Toast.LENGTH_SHORT).show();
                return;
            }

            // 如果正在扫描，先停止
            if (bluetoothAdapter.isDiscovering()) {
                bluetoothAdapter.cancelDiscovery();
                Toast.makeText(this, "⏹️ 已停止当前扫描", Toast.LENGTH_SHORT).show();

                // 等待一下再开始新扫描
                new android.os.Handler().postDelayed(() -> {
                    startActualScan(bluetoothAdapter);
                }, 1000);
            } else {
                startActualScan(bluetoothAdapter);
            }

        } catch (Exception e) {
            Toast.makeText(this, "❌ 蓝牙扫描失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    /**
     * 执行实际的蓝牙扫描
     */
    private void startActualScan(BluetoothAdapter bluetoothAdapter) {
        try {
            boolean started = bluetoothAdapter.startDiscovery();

            if (started) {
                Toast.makeText(this, "🔍 蓝牙扫描已启动\n" +
                        "目标MAC: " + currentTargetMac + "\n" +
                        "请查看日志观察Hook状态", Toast.LENGTH_LONG).show();

                // 10秒后自动停止扫描
                new android.os.Handler().postDelayed(() -> {
                    try {
                        if (bluetoothAdapter.isDiscovering()) {
                            bluetoothAdapter.cancelDiscovery();
                            Toast.makeText(this, "⏹️ 蓝牙扫描已停止", Toast.LENGTH_SHORT).show();
                        }
                    } catch (Exception e) {
                        // 忽略停止扫描的错误
                    }
                }, 10000);

            } else {
                Toast.makeText(this, "❌ 蓝牙扫描启动失败", Toast.LENGTH_SHORT).show();
            }

        } catch (Exception e) {
            Toast.makeText(this, "❌ 启动扫描异常: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    /**
     * 显示设置MAC地址对话框
     */
    private void showSetMacDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("⚙️ 设置目标MAC地址");

        // 创建输入框
        EditText editText = new EditText(this);
        editText.setText(currentTargetMac);
        editText.setHint("请输入MAC地址 (格式: AA:BB:CC:DD:EE:FF)");
        editText.setPadding(32, 32, 32, 32);

        builder.setView(editText);

        builder.setMessage("当前MAC地址: " + currentTargetMac + "\n\n" +
                "请输入新的目标MAC地址。\n" +
                "格式示例: 32:EB:17:06:38:EF\n\n" +
                "⚠️ 修改后需要重启手机才能生效！");

        builder.setPositiveButton("💾 保存", (dialog, which) -> {
            String newMac = editText.getText().toString().trim().toUpperCase();
            if (isValidMacAddress(newMac)) {
                // 保存到SharedPreferences
                prefs.edit().putString(KEY_TARGET_MAC, newMac).apply();
                currentTargetMac = newMac;

                // 同时保存到多个位置以确保Hook能读取到
                saveConfigToMultipleLocations(newMac);

                Toast.makeText(this, "✅ MAC地址已保存: " + newMac + "\n已保存到多个位置，请重启手机使配置生效", Toast.LENGTH_LONG).show();

                // 重新创建UI以显示新的MAC地址
                recreate();
            } else {
                Toast.makeText(this, "❌ MAC地址格式错误！\n正确格式: AA:BB:CC:DD:EE:FF", Toast.LENGTH_LONG).show();
            }
        });

        builder.setNegativeButton("❌ 取消", null);

        builder.setNeutralButton("🔄 重置为默认", (dialog, which) -> {
            prefs.edit().putString(KEY_TARGET_MAC, DEFAULT_MAC).apply();
            currentTargetMac = DEFAULT_MAC;

            // 同时保存到多个位置
            saveConfigToMultipleLocations(DEFAULT_MAC);

            Toast.makeText(this, "✅ 已重置为默认MAC: " + DEFAULT_MAC + "\n请重启手机使配置生效", Toast.LENGTH_LONG).show();
            recreate();
        });

        builder.show();
    }

    /**
     * 保存配置到多个位置
     */
    private void saveConfigToMultipleLocations(String mac) {
        // 保存位置列表
        String[] configPaths = {
            "/storage/emulated/0/Android/data/com.example.bluetoothscanner/files/config.txt",
            "/sdcard/bluetooth_hook_config.txt"
        };

        int successCount = 0;

        for (String configPath : configPaths) {
            try {
                java.io.File configFile = new java.io.File(configPath);
                java.io.File parentDir = configFile.getParentFile();

                // 确保目录存在
                if (parentDir != null && !parentDir.exists()) {
                    parentDir.mkdirs();
                }

                // 写入配置
                try (java.io.FileWriter writer = new java.io.FileWriter(configFile)) {
                    writer.write("# 蓝牙Hook配置文件\n");
                    writer.write("# 修改target_mac后重启手机生效\n");
                    writer.write("# 格式: target_mac=AA:BB:CC:DD:EE:FF\n");
                    writer.write("target_mac=" + mac + "\n");
                    writer.write("# 保存时间: " + new java.util.Date() + "\n");
                    writer.flush();
                }

                successCount++;

            } catch (Exception e) {
                // 继续尝试下一个位置
            }
        }

        // 尝试保存到系统可访问的位置（需要Root权限）
        try {
            java.io.File systemConfig = new java.io.File("/data/local/tmp/bluetooth_hook_config.txt");
            try (java.io.FileWriter writer = new java.io.FileWriter(systemConfig)) {
                writer.write("target_mac=" + mac + "\n");
                writer.flush();
            }
            successCount++;
        } catch (Exception e) {
            // Root权限可能不可用，忽略
        }

        if (successCount > 0) {
            Toast.makeText(this, "✅ 配置已保存到 " + successCount + " 个位置", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 验证MAC地址格式
     */
    private boolean isValidMacAddress(String mac) {
        if (mac == null || mac.length() != 17) return false;
        return mac.matches("^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$");
    }

    /**
     * 查看日志
     */
    private void viewLog() {
        StringBuilder allContent = new StringBuilder();
        allContent.append("=== 蓝牙Hook日志监控 ===\n\n");

        // 检查简单日志文件
        String simpleLogPath = "/sdcard/bluetooth_hook_simple.log";
        java.io.File simpleLogFile = new java.io.File(simpleLogPath);

        boolean hasFileLog = false;

        // 检查简单日志文件
        if (simpleLogFile.exists() && simpleLogFile.length() > 0) {
            hasFileLog = true;
            allContent.append("📁 找到简单日志文件: ").append(simpleLogPath).append("\n");
            allContent.append("📏 文件大小: ").append(simpleLogFile.length()).append(" 字节\n");
            allContent.append("⏰ 最后修改: ").append(new java.util.Date(simpleLogFile.lastModified())).append("\n\n");

            allContent.append("=== 简单日志内容 (最新30行) ===\n");

            try (java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.FileReader(simpleLogFile))) {
                java.util.List<String> lines = new java.util.ArrayList<>();
                String line;

                // 读取所有行
                while ((line = reader.readLine()) != null) {
                    lines.add(line);
                }

                // 显示最后30行
                int startIndex = Math.max(0, lines.size() - 30);
                for (int i = startIndex; i < lines.size(); i++) {
                    allContent.append(lines.get(i)).append("\n");
                }

                allContent.append("\n=== 简单日志统计 ===\n");
                allContent.append("📊 总行数: ").append(lines.size()).append("\n");
                allContent.append("📋 显示行数: ").append(lines.size() - startIndex).append("\n\n");

            } catch (Exception e) {
                allContent.append("❌ 读取简单日志失败: ").append(e.getMessage()).append("\n\n");
            }
        }

        // 显示LSPosed日志说明
        allContent.append("=== 主要日志位置 ===\n");
        allContent.append("📋 LSPosed日志: 在LSPosed管理器中查看\n");
        allContent.append("🔍 搜索关键词: BluetoothHook\n");
        allContent.append("📱 或使用命令: adb logcat | grep BluetoothHook\n\n");

        if (!hasFileLog) {
            allContent.append("⚠️ 简单日志文件不存在或为空\n\n");
            allContent.append("这是正常的，主要日志在LSPosed中。\n\n");
            allContent.append("如果Hook正常工作，你应该能在LSPosed日志中看到：\n");
            allContent.append("• ✅ BluetoothAdapter多方法Hook已安装\n");
            allContent.append("• ✅ BluetoothDevice多方法Hook已安装\n");
            allContent.append("• 📍 设备getAddress调用: [MAC地址]\n");
            allContent.append("• 📱 设备getName调用: [设备名] ([MAC地址])\n");
            allContent.append("• 🎯 发现目标设备: ").append(currentTargetMac).append(", RSSI: [信号强度] dBm\n\n");

            allContent.append("💡 提示：\n");
            allContent.append("• 如果看到很多📍 getAddress调用，说明Hook正常工作\n");
            allContent.append("• 可以从日志中找到附近设备的MAC地址\n");
            allContent.append("• 然后使用'⚙️ 设置MAC'功能设置为目标设备\n\n");
        }
        
        // 添加Hook状态信息
        allContent.append("\n=== Hook状态 ===\n");
        allContent.append("🔍 模块激活: ").append(isModuleActive() ? "✅ 已激活" : "❌ 未激活").append("\n");
        allContent.append("🎯 目标MAC: ").append(currentTargetMac).append("\n");
        allContent.append("📶 RSSI范围: [-80, -20] dBm\n");
        
        // 显示日志内容
        showLogDialog(allContent.toString());
    }
    
    /**
     * 显示重启对话框
     */
    private void showRestartDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("🔄 重启Hook");
        builder.setMessage("重启蓝牙进程以重新加载Hook\n\n" +
                "⚠️ 注意：需要Root权限\n" +
                "是否继续？");
        
        builder.setPositiveButton("🔄 重启", (dialog, which) -> {
            Toast.makeText(this, "重启功能需要Root权限，请手动重启设备", Toast.LENGTH_LONG).show();
        });
        
        builder.setNegativeButton("❌ 取消", null);
        builder.show();
    }
    
    /**
     * 显示日志对话框
     */
    private void showLogDialog(String content) {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("📋 Hook运行日志");
        
        if (content.trim().isEmpty()) {
            builder.setMessage("日志文件为空\n\n请确保蓝牙已开启并尝试扫描设备");
        } else {
            // 创建滚动文本视图
            TextView textView = new TextView(this);
            textView.setText(content);
            textView.setTextSize(10);
            textView.setPadding(16, 16, 16, 16);
            textView.setTextIsSelectable(true);
            textView.setTypeface(android.graphics.Typeface.MONOSPACE);
            
            ScrollView scrollView = new ScrollView(this);
            scrollView.addView(textView);
            
            builder.setView(scrollView);
        }
        
        builder.setPositiveButton("🔄 刷新", (dialog, which) -> {
            dialog.dismiss();
            viewLog();
        });
        builder.setNegativeButton("✅ 关闭", null);
        
        AlertDialog dialog = builder.create();
        dialog.show();
        
        // 设置对话框大小
        if (dialog.getWindow() != null) {
            dialog.getWindow().setLayout(
                (int) (getResources().getDisplayMetrics().widthPixels * 0.9),
                (int) (getResources().getDisplayMetrics().heightPixels * 0.8)
            );
        }
    }
}

@echo off
echo ========================================
echo 蓝牙修复脚本 - 紧急恢复
echo ========================================
echo.

echo ⚠️ 如果蓝牙无法打开，请按照以下步骤操作：
echo.

echo 1. 检查设备连接...
adb devices
echo.

echo 2. 在LSPosed中禁用蓝牙扫描器模块...
echo 请手动操作：
echo - 打开LSPosed管理器
echo - 找到"蓝牙扫描器"模块
echo - 关闭模块开关（变为灰色）
echo - 点击"重启"或手动重启设备
echo.
pause

echo 3. 重启设备以恢复蓝牙功能...
echo 是否立即重启设备? (Y/N)
set /p reboot_choice=
if /i "%reboot_choice%"=="Y" (
    echo 正在重启设备...
    adb reboot
    echo 设备重启中，请等待...
    timeout /t 30 >nul
    echo 等待设备重新连接...
    adb wait-for-device
    timeout /t 10 >nul
)
echo.

echo 4. 检查蓝牙状态...
echo 检查蓝牙是否已恢复正常...
adb shell "dumpsys bluetooth_manager | grep 'Bluetooth Status'"
echo.

echo 5. 清理Hook相关进程...
echo 确保没有残留的Hook进程...
adb shell "ps | grep bluetooth"
echo.

echo ========================================
echo 🔧 蓝牙恢复指南：
echo.
echo ✅ 如果蓝牙已恢复：
echo 1. 蓝牙应该可以正常开启/关闭
echo 2. 可以正常扫描和连接设备
echo 3. 模块已安全禁用
echo.
echo ❌ 如果蓝牙仍有问题：
echo 1. 完全卸载蓝牙扫描器APK
echo 2. 重启设备
echo 3. 检查是否有其他Xposed模块影响蓝牙
echo 4. 考虑恢复出厂设置（最后手段）
echo.
echo 💡 重新启用模块：
echo 1. 确保使用修复后的安全版本
echo 2. 在LSPosed中重新启用
echo 3. 只勾选 com.android.bluetooth 进程
echo 4. 重启设备
echo ========================================
echo.

echo 按任意键退出...
pause >nul

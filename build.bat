@echo off
echo ========================================
echo    Android APK Builder
echo ========================================

echo.
echo Checking Java...
java -version
if %errorlevel% neq 0 (
    echo ERROR: Java not found!
    echo Please install Java 11+ from: https://adoptium.net/
    pause
    exit /b 1
)

echo.
echo Building APK...
call gradlew assembleDebug

if %errorlevel% equ 0 (
    echo.
    echo SUCCESS! APK built successfully!
    echo Location: app\build\outputs\apk\debug\app-debug.apk
    echo.
    if exist "app\build\outputs\apk\debug\app-debug.apk" (
        echo Open folder? (Y/N)
        set /p choice=
        if /i "%choice%"=="Y" (
            explorer app\build\outputs\apk\debug\
        )
    )
) else (
    echo.
    echo FAILED! Build error occurred.
    echo Try upgrading to Java 11+ or check network connection.
)

echo.
pause

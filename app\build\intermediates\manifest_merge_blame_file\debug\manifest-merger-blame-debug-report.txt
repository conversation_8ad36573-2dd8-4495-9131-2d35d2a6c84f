1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.bluetoothscanner.debug"
4    android:versionCode="1"
5    android:versionName="1.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
8-->C:\xposedshell\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->C:\xposedshell\app\src\main\AndroidManifest.xml
10
11    <!-- Android 15兼容权限配置 -->
12    <!-- 传统蓝牙权限 (API < 31) -->
13    <uses-permission
13-->C:\xposedshell\app\src\main\AndroidManifest.xml:7:5-8:38
14        android:name="android.permission.BLUETOOTH"
14-->C:\xposedshell\app\src\main\AndroidManifest.xml:7:22-65
15        android:maxSdkVersion="30" />
15-->C:\xposedshell\app\src\main\AndroidManifest.xml:8:9-35
16    <uses-permission
16-->C:\xposedshell\app\src\main\AndroidManifest.xml:9:5-10:38
17        android:name="android.permission.BLUETOOTH_ADMIN"
17-->C:\xposedshell\app\src\main\AndroidManifest.xml:9:22-71
18        android:maxSdkVersion="30" />
18-->C:\xposedshell\app\src\main\AndroidManifest.xml:10:9-35
19
20    <!-- 新蓝牙权限 (API >= 31, Android 12+) -->
21    <uses-permission
21-->C:\xposedshell\app\src\main\AndroidManifest.xml:13:5-14:58
22        android:name="android.permission.BLUETOOTH_SCAN"
22-->C:\xposedshell\app\src\main\AndroidManifest.xml:13:22-70
23        android:usesPermissionFlags="neverForLocation" />
23-->C:\xposedshell\app\src\main\AndroidManifest.xml:14:9-55
24    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
24-->C:\xposedshell\app\src\main\AndroidManifest.xml:15:5-76
24-->C:\xposedshell\app\src\main\AndroidManifest.xml:15:22-73
25    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
25-->C:\xposedshell\app\src\main\AndroidManifest.xml:16:5-78
25-->C:\xposedshell\app\src\main\AndroidManifest.xml:16:22-75
26
27    <!-- 位置权限 -->
28    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
28-->C:\xposedshell\app\src\main\AndroidManifest.xml:19:5-79
28-->C:\xposedshell\app\src\main\AndroidManifest.xml:19:22-76
29    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
29-->C:\xposedshell\app\src\main\AndroidManifest.xml:20:5-81
29-->C:\xposedshell\app\src\main\AndroidManifest.xml:20:22-78
30
31    <!-- 网络权限 -->
32    <uses-permission android:name="android.permission.INTERNET" />
32-->C:\xposedshell\app\src\main\AndroidManifest.xml:23:5-67
32-->C:\xposedshell\app\src\main\AndroidManifest.xml:23:22-64
33    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
33-->C:\xposedshell\app\src\main\AndroidManifest.xml:24:5-79
33-->C:\xposedshell\app\src\main\AndroidManifest.xml:24:22-76
34
35    <!-- 进程管理权限 -->
36    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
36-->C:\xposedshell\app\src\main\AndroidManifest.xml:27:5-84
36-->C:\xposedshell\app\src\main\AndroidManifest.xml:27:22-81
37
38    <!-- 其他权限 -->
39    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
39-->C:\xposedshell\app\src\main\AndroidManifest.xml:30:5-77
39-->C:\xposedshell\app\src\main\AndroidManifest.xml:30:22-74
40    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
40-->C:\xposedshell\app\src\main\AndroidManifest.xml:31:5-77
40-->C:\xposedshell\app\src\main\AndroidManifest.xml:31:22-74
41    <uses-permission android:name="android.permission.WAKE_LOCK" />
41-->C:\xposedshell\app\src\main\AndroidManifest.xml:32:5-68
41-->C:\xposedshell\app\src\main\AndroidManifest.xml:32:22-65
42
43    <!-- 硬件特性声明 -->
44    <uses-feature
44-->C:\xposedshell\app\src\main\AndroidManifest.xml:35:5-90
45        android:name="android.hardware.bluetooth_le"
45-->C:\xposedshell\app\src\main\AndroidManifest.xml:35:19-63
46        android:required="true" />
46-->C:\xposedshell\app\src\main\AndroidManifest.xml:35:64-87
47    <uses-feature
47-->C:\xposedshell\app\src\main\AndroidManifest.xml:36:5-87
48        android:name="android.hardware.bluetooth"
48-->C:\xposedshell\app\src\main\AndroidManifest.xml:36:19-60
49        android:required="true" />
49-->C:\xposedshell\app\src\main\AndroidManifest.xml:36:61-84
50    <uses-feature
50-->C:\xposedshell\app\src\main\AndroidManifest.xml:37:5-87
51        android:name="android.hardware.location"
51-->C:\xposedshell\app\src\main\AndroidManifest.xml:37:19-59
52        android:required="false" />
52-->C:\xposedshell\app\src\main\AndroidManifest.xml:37:60-84
53
54    <permission
54-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
55        android:name="com.example.bluetoothscanner.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
55-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
56        android:protectionLevel="signature" />
56-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
57
58    <uses-permission android:name="com.example.bluetoothscanner.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
58-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
58-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
59
60    <application
60-->C:\xposedshell\app\src\main\AndroidManifest.xml:39:5-68:19
61        android:allowBackup="true"
61-->C:\xposedshell\app\src\main\AndroidManifest.xml:40:9-35
62        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
62-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
63        android:debuggable="true"
64        android:extractNativeLibs="false"
65        android:icon="@mipmap/ic_launcher"
65-->C:\xposedshell\app\src\main\AndroidManifest.xml:41:9-43
66        android:label="@string/app_name"
66-->C:\xposedshell\app\src\main\AndroidManifest.xml:42:9-41
67        android:roundIcon="@mipmap/ic_launcher_round"
67-->C:\xposedshell\app\src\main\AndroidManifest.xml:43:9-54
68        android:supportsRtl="true"
68-->C:\xposedshell\app\src\main\AndroidManifest.xml:44:9-35
69        android:theme="@style/Theme.BluetoothScanner" >
69-->C:\xposedshell\app\src\main\AndroidManifest.xml:45:9-54
70
71        <!-- 主活动 -->
72        <activity
72-->C:\xposedshell\app\src\main\AndroidManifest.xml:49:9-56:20
73            android:name="com.example.bluetoothscanner.MainActivity"
73-->C:\xposedshell\app\src\main\AndroidManifest.xml:50:13-41
74            android:exported="true" >
74-->C:\xposedshell\app\src\main\AndroidManifest.xml:51:13-36
75            <intent-filter>
75-->C:\xposedshell\app\src\main\AndroidManifest.xml:52:13-55:29
76                <action android:name="android.intent.action.MAIN" />
76-->C:\xposedshell\app\src\main\AndroidManifest.xml:53:17-69
76-->C:\xposedshell\app\src\main\AndroidManifest.xml:53:25-66
77
78                <category android:name="android.intent.category.LAUNCHER" />
78-->C:\xposedshell\app\src\main\AndroidManifest.xml:54:17-77
78-->C:\xposedshell\app\src\main\AndroidManifest.xml:54:27-74
79            </intent-filter>
80        </activity>
81
82        <!-- Xposed 模块元数据 -->
83        <meta-data
83-->C:\xposedshell\app\src\main\AndroidManifest.xml:59:9-61:36
84            android:name="xposedmodule"
84-->C:\xposedshell\app\src\main\AndroidManifest.xml:60:13-40
85            android:value="true" />
85-->C:\xposedshell\app\src\main\AndroidManifest.xml:61:13-33
86        <meta-data
86-->C:\xposedshell\app\src\main\AndroidManifest.xml:62:9-64:48
87            android:name="xposeddescription"
87-->C:\xposedshell\app\src\main\AndroidManifest.xml:63:13-45
88            android:value="蓝牙扫描自动执行curl命令模块" />
88-->C:\xposedshell\app\src\main\AndroidManifest.xml:64:13-45
89        <meta-data
89-->C:\xposedshell\app\src\main\AndroidManifest.xml:65:9-67:34
90            android:name="xposedminversion"
90-->C:\xposedshell\app\src\main\AndroidManifest.xml:66:13-44
91            android:value="54" />
91-->C:\xposedshell\app\src\main\AndroidManifest.xml:67:13-31
92
93        <provider
93-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
94            android:name="androidx.startup.InitializationProvider"
94-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
95            android:authorities="com.example.bluetoothscanner.debug.androidx-startup"
95-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
96            android:exported="false" >
96-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
97            <meta-data
97-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
98                android:name="androidx.emoji2.text.EmojiCompatInitializer"
98-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
99                android:value="androidx.startup" />
99-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
100            <meta-data
100-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\8af27606e6293b3b18c49e9b214e0d7e\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
101                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
101-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\8af27606e6293b3b18c49e9b214e0d7e\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
102                android:value="androidx.startup" />
102-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\8af27606e6293b3b18c49e9b214e0d7e\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
103        </provider>
104    </application>
105
106</manifest>

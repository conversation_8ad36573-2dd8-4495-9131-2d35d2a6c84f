# 🚀 快速开始指南

## 📋 前置要求

- ✅ 已Root的Android设备（Android 6.0+）
- ✅ 已安装LSPosed框架
- ✅ 开发环境：JDK 8+ 和 Android Studio（可选）

## 🔧 5分钟快速构建

### 方法A：Android Studio（推荐）

#### 步骤1：检查项目
```bash
# Windows用户
双击 check-project.bat

# 确认看到 "✅ 项目文件完整！"
```

#### 步骤2：启动Android Studio
```bash
# Windows用户
双击 open-with-android-studio.bat

# 或手动启动Android Studio并打开项目文件夹
```

#### 步骤3：构建APK
1. 等待Android Studio项目同步完成
2. 点击 `Build` → `Build Bundle(s) / APK(s)` → `Build APK(s)`
3. 等待构建完成

### 方法B：命令行构建（需要环境配置）

#### 步骤1：检查环境
```bash
# Windows用户
双击 setup.bat

# 查看环境检查结果
```

#### 步骤2：构建APK
```bash
# Windows用户
双击 build.bat

# 按照提示操作
```

### 步骤3：安装到设备
```bash
# 连接设备，启用USB调试
adb install app\build\outputs\apk\debug\app-debug.apk
```

### 步骤4：启用模块
1. 打开LSPosed管理器
2. 找到"蓝牙扫描器"模块
3. 启用模块并重启设备

### 步骤5：配置使用
1. 打开蓝牙扫描器应用
2. 授予所需权限
3. 配置目标MAC地址和curl命令
4. 点击"开始扫描"

## 🎯 默认配置

```
目标MAC地址: C3:4C:A1:E8:14:27
信号强度范围: -70 到 -10 dBm
设备出现curl: curl -X POST https://example.com/api/present
设备消失curl: curl -X POST https://example.com/api/absent
```

## 🔍 测试验证

1. **测试curl命令**
   - 在应用中点击"测试curl"按钮
   - 查看执行结果

2. **测试蓝牙扫描**
   - 确保目标设备蓝牙开启
   - 观察应用中的设备状态显示

3. **查看日志**
   - 应用内日志：实时显示运行状态
   - 系统日志：`adb logcat | grep BluetoothScanner`

## ❗ 常见问题

### 构建失败
- **问题**: "找不到Java环境"
- **解决**: 安装JDK 8+ 并配置PATH环境变量

### 权限问题
- **问题**: 扫描不到设备
- **解决**: 确保已授予蓝牙和位置权限

### 模块不生效
- **问题**: Hook不工作
- **解决**: 确认LSPosed中已启用模块并重启设备

## 📞 获取帮助

1. 查看详细文档：[README.md](README.md)
2. Android Studio指南：[ANDROID_STUDIO_GUIDE.md](ANDROID_STUDIO_GUIDE.md)
3. 检查应用内日志和系统日志

---

**🎉 恭喜！你已经成功构建并安装了蓝牙扫描器Xposed模块！**

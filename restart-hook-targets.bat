@echo off
echo ========================================
echo 安全重启Hook目标脚本 (改进版)
echo ========================================
echo.

echo 1. 检查设备连接...
adb devices
echo.

echo 2. 检查Root权限...
adb shell "su -c 'id'"
if %errorlevel% neq 0 (
    echo ❌ 设备没有Root权限或未授权
    echo 请确保设备已Root并授权ADB使用Root权限
    pause
    exit /b 1
)
echo ✅ Root权限检查通过
echo.

echo 3. 备份当前日志...
adb shell "cp /storage/emulated/0/Download/bluetooth_hook.log /storage/emulated/0/Download/bluetooth_hook_backup.log" 2>nul
echo.

echo 4. 安全重启蓝牙进程 (只重启进程，保护蓝牙功能)...
echo 正在重启com.android.bluetooth进程...
adb shell "su -c 'pkill -f com.android.bluetooth'"
timeout /t 2 >nul

echo 正在重新启动蓝牙服务...
adb shell "su -c 'am start-service -n com.android.bluetooth/.btservice.AdapterService'"
timeout /t 1 >nul

echo 正在触发蓝牙适配器重新初始化...
adb shell "su -c 'am broadcast -a android.bluetooth.adapter.action.REQUEST_ENABLE'"
timeout /t 1 >nul
echo ✅ 蓝牙进程已安全重启
echo.

echo 5. 重新加载Hook模块...
echo 正在触发Hook模块重新初始化...
adb shell "su -c 'am start -n com.example.bluetoothscanner/.MainActivity --ez reload_hook true'"
timeout /t 2 >nul
echo ✅ Hook模块重新加载完成
echo.

echo 6. 等待Hook重新初始化...
echo 等待5秒让Hook安全重新加载...
timeout /t 5 >nul
echo.

echo 8. 检查Hook是否重新加载...
echo 查找Hook重新加载日志:
adb logcat -d | findstr "Hook重新加载\|Hook初始化" | tail -5
echo.

echo 9. 检查新的日志文件...
echo 新的Hook日志:
adb shell "tail -10 /storage/emulated/0/Download/bluetooth_hook.log"
echo.

echo 10. 启动APP查看状态...
adb shell "am start -n com.example.bluetoothscanner/.MainActivity"
echo.

echo ========================================
echo 🔄 安全重启完成！
echo.
echo ✅ 已执行的安全操作：
echo 1. 安全刷新了蓝牙服务 (未强制杀死进程)
echo 2. 重新加载了Hook模块
echo 3. 触发了Hook重新初始化
echo 4. 保护了蓝牙核心功能
echo.
echo 💡 验证方法：
echo 1. 检查蓝牙是否仍能正常开启/关闭
echo 2. 打开APP查看模块状态
echo 3. 查看日志中的重新加载时间
echo 4. 测试蓝牙设备检测功能
echo.
echo 🔍 如果Hook仍未工作：
echo 1. 检查LSPosed中模块是否仍然启用
echo 2. 确认作用域配置正确
echo 3. 尝试重启整个设备
echo.
echo ⚠️ 如果蓝牙出现问题：
echo 1. 立即运行 fix-bluetooth.bat
echo 2. 在LSPosed中禁用模块
echo 3. 重启设备恢复蓝牙功能
echo ========================================
echo.

echo 按任意键开始实时监控新的Hook日志...
pause >nul

echo.
echo 🔍 实时监控Hook日志:
echo ========================================
adb logcat -c
adb logcat | findstr "BluetoothHook"

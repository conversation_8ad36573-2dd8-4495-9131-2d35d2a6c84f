@echo off
chcp 65001 >nul
echo ========================================
echo    Android APK Build Script
echo ========================================

echo.
echo [1/4] Checking Java environment...
java -version
if %errorlevel% neq 0 (
    echo Error: Java not found, please install Java 11 or higher
    pause
    exit /b 1
)

echo.
echo [2/4] Cleaning project...
call gradlew clean
if %errorlevel% neq 0 (
    echo Error: Clean failed
    pause
    exit /b 1
)

echo.
echo [3/4] Building Debug APK...
call gradlew assembleDebug
if %errorlevel% neq 0 (
    echo Error: Build failed
    pause
    exit /b 1
)

echo.
echo [4/4] Build completed!
echo.
echo APK location:
echo Debug APK: app\build\outputs\apk\debug\app-debug.apk
echo.

if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo Build successful! APK generated
    echo.
    echo Open APK folder? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        explorer app\build\outputs\apk\debug\
    )
) else (
    echo Build failed, APK file not found
)

echo.
pause

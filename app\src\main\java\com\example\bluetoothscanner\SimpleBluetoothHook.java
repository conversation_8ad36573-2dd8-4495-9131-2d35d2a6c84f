package com.example.bluetoothscanner;

import android.bluetooth.BluetoothDevice;
import android.content.Intent;

import java.io.File;
import java.io.FileWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;

/**
 * 极简安全蓝牙Hook - 只监听广播，绝不干扰蓝牙功能
 */
public class SimpleBluetoothHook {
    private static final String TAG = "BluetoothHook";
    
    // 配置参数 - 从SharedPreferences读取
    private static String TARGET_MAC = "32:EB:17:06:38:EF"; // 默认值
    private static final int MIN_RSSI = -80;
    private static final int MAX_RSSI = -20;
    private static final String CURL_APPEAR = "curl -X POST https://httpbin.org/post -d 'device_appear=true'";
    private static final String CURL_DISAPPEAR = "curl -X POST https://httpbin.org/post -d 'device_disappear=true'";

    // SharedPreferences相关
    private static final String PREFS_NAME = "bluetooth_hook_config";
    private static final String KEY_TARGET_MAC = "target_mac";
    
    // 日志相关 - 简化为只使用Xposed日志
    private static final SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
    private static final String SIMPLE_LOG_FILE = "/sdcard/bluetooth_hook_simple.log";
    
    // 设备状态
    private static final Map<String, Boolean> devicePresent = new HashMap<>();
    private static final Map<String, Long> lastLogTime = new HashMap<>();
    
    /**
     * 读取配置 - 使用多种方式
     */
    private static void loadConfig() {
        try {
            // 尝试多个可能的配置文件路径
            String[] configPaths = {
                "/data/data/com.example.bluetoothscanner/shared_prefs/" + PREFS_NAME + ".xml",
                "/storage/emulated/0/Android/data/com.example.bluetoothscanner/files/config.txt",
                "/sdcard/bluetooth_hook_config.txt",
                "/data/local/tmp/bluetooth_hook_config.txt"
            };

            boolean configLoaded = false;

            for (String configPath : configPaths) {
                java.io.File configFile = new java.io.File(configPath);
                if (configFile.exists()) {
                    try {
                        if (configPath.endsWith(".xml")) {
                            // XML格式解析
                            configLoaded = loadFromXml(configFile);
                        } else {
                            // 简单文本格式解析
                            configLoaded = loadFromText(configFile);
                        }

                        if (configLoaded) {
                            log("✅ 已从配置文件加载MAC: " + TARGET_MAC + " (路径: " + configPath + ")");
                            break;
                        }
                    } catch (Exception e) {
                        log("⚠️ 读取配置文件失败: " + configPath + " - " + e.getMessage());
                    }
                }
            }

            if (!configLoaded) {
                log("📝 未找到配置文件，使用默认MAC: " + TARGET_MAC);
                // 尝试创建简单配置文件
                createSimpleConfig();
            }

        } catch (Exception e) {
            log("❌ 读取配置失败: " + e.getMessage() + "，使用默认MAC: " + TARGET_MAC);
        }
    }

    /**
     * 从XML文件加载配置
     */
    private static boolean loadFromXml(java.io.File configFile) {
        try (java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.FileReader(configFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.contains("name=\"" + KEY_TARGET_MAC + "\"")) {
                    // 提取MAC地址
                    int start = line.indexOf("value=\"") + 7;
                    int end = line.indexOf("\"", start);
                    if (start > 6 && end > start) {
                        String newMac = line.substring(start, end);
                        if (isValidMacAddress(newMac)) {
                            TARGET_MAC = newMac;
                            return true;
                        } else {
                            log("⚠️ 配置中的MAC地址格式无效: " + newMac);
                        }
                    }
                    break;
                }
            }
        } catch (Exception e) {
            log("❌ XML配置解析失败: " + e.getMessage());
        }
        return false;
    }

    /**
     * 从文本文件加载配置
     */
    private static boolean loadFromText(java.io.File configFile) {
        try (java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.FileReader(configFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.startsWith("target_mac=")) {
                    String newMac = line.substring(11).trim();
                    if (isValidMacAddress(newMac)) {
                        TARGET_MAC = newMac;
                        return true;
                    } else {
                        log("⚠️ 配置中的MAC地址格式无效: " + newMac);
                    }
                }
            }
        } catch (Exception e) {
            log("❌ 文本配置解析失败: " + e.getMessage());
        }
        return false;
    }

    /**
     * 创建简单配置文件
     */
    private static void createSimpleConfig() {
        try {
            java.io.File configFile = new java.io.File("/sdcard/bluetooth_hook_config.txt");
            try (java.io.FileWriter writer = new java.io.FileWriter(configFile)) {
                writer.write("# 蓝牙Hook配置文件\n");
                writer.write("# 修改target_mac后重启手机生效\n");
                writer.write("target_mac=" + TARGET_MAC + "\n");
                writer.flush();
            }
            log("📝 已创建配置文件: /sdcard/bluetooth_hook_config.txt");
        } catch (Exception e) {
            // 静默忽略创建失败
        }
    }

    /**
     * 验证MAC地址格式
     */
    private static boolean isValidMacAddress(String mac) {
        if (mac == null || mac.length() != 17) return false;
        return mac.matches("^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$");
    }

    /**
     * 初始化Hook - 极简安全版本
     */
    public static void init(ClassLoader classLoader, String packageName) {
        XposedBridge.log(TAG + ": ========== 极简安全蓝牙Hook启动 ==========");
        XposedBridge.log(TAG + ": 📦 进程: " + packageName);

        // 加载配置
        loadConfig();

        XposedBridge.log(TAG + ": 🎯 目标MAC: " + TARGET_MAC);
        XposedBridge.log(TAG + ": 📶 RSSI范围: [" + MIN_RSSI + ", " + MAX_RSSI + "]");

        log("========== 极简安全蓝牙Hook启动 ==========");
        log("📦 进程: " + packageName);
        log("🎯 目标MAC: " + TARGET_MAC);
        log("📶 RSSI范围: [" + MIN_RSSI + ", " + MAX_RSSI + "] dBm");
        
        // 只安装最安全的广播Hook
        boolean hookSuccess = hookBluetoothBroadcastOnly(classLoader);
        
        if (hookSuccess) {
            log("✅ 极简Hook安装成功 - 只监听广播，绝不干扰蓝牙");
            startMonitor();
        } else {
            log("❌ Hook安装失败");
        }
        
        log("========== Hook初始化完成 ==========");
        XposedBridge.log(TAG + ": ========== Hook初始化完成 ==========");
    }
    
    /**
     * 极简蓝牙广播Hook - Hook具体实现类
     */
    private static boolean hookBluetoothBroadcastOnly(ClassLoader classLoader) {
        boolean success = false;

        // 尝试Hook多个可能的实现类
        String[] contextImplClasses = {
            "android.app.ContextImpl",
            "android.content.ContextWrapper"
        };

        for (String className : contextImplClasses) {
            try {
                Class<?> contextImplClass = XposedHelpers.findClass(className, classLoader);
                XposedHelpers.findAndHookMethod(contextImplClass, "sendBroadcast", Intent.class, new XC_MethodHook() {
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        // 在广播发送后监听，绝不阻止或修改广播
                        try {
                            Intent intent = (Intent) param.args[0];
                            if (intent != null && BluetoothDevice.ACTION_FOUND.equals(intent.getAction())) {
                                BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                                if (device != null) {
                                    String mac = device.getAddress();

                                    // 只处理目标MAC地址
                                    if (TARGET_MAC.equalsIgnoreCase(mac)) {
                                        String name = device.getName();
                                        int rssi = intent.getShortExtra(BluetoothDevice.EXTRA_RSSI, Short.MIN_VALUE);

                                        log("🎯 发现目标设备: " + name + " (" + mac + "), RSSI: " + rssi + " dBm");
                                        checkDevice(mac, name, rssi);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            // 静默忽略所有错误，绝不影响广播系统
                        }
                    }
                });

                log("✅ " + className + " 广播Hook已安装");
                success = true;

            } catch (Exception e) {
                log("❌ " + className + " Hook失败: " + e.getMessage());
            }
        }

        // 无论广播Hook是否成功，都尝试Hook更多蓝牙方法
        try {
            Class<?> adapterClass = XposedHelpers.findClass("android.bluetooth.BluetoothAdapter", classLoader);

            // Hook startDiscovery
            XposedHelpers.findAndHookMethod(adapterClass, "startDiscovery", new XC_MethodHook() {
                @Override
                protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                    log("🔍 蓝牙开始扫描 - startDiscovery被调用");
                }

                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    boolean result = (Boolean) param.getResult();
                    log("🔍 startDiscovery结果: " + result);
                }
            });

            // Hook cancelDiscovery
            XposedHelpers.findAndHookMethod(adapterClass, "cancelDiscovery", new XC_MethodHook() {
                @Override
                protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                    log("⏹️ 蓝牙停止扫描 - cancelDiscovery被调用");
                }
            });

            // Hook isDiscovering
            XposedHelpers.findAndHookMethod(adapterClass, "isDiscovering", new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    boolean result = (Boolean) param.getResult();
                    log("❓ 蓝牙扫描状态查询: " + (result ? "正在扫描" : "未在扫描"));
                }
            });

            log("✅ BluetoothAdapter多方法Hook已安装");
            success = true;

        } catch (Exception e) {
            log("❌ BluetoothAdapter Hook失败: " + e.getMessage());
        }

        // Hook BluetoothDevice相关方法
        try {
            Class<?> deviceClass = XposedHelpers.findClass("android.bluetooth.BluetoothDevice", classLoader);

            // Hook getName - 只处理目标MAC
            XposedHelpers.findAndHookMethod(deviceClass, "getName", new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    if (param.thisObject instanceof BluetoothDevice) {
                        BluetoothDevice device = (BluetoothDevice) param.thisObject;
                        String mac = device.getAddress();

                        // 只处理目标MAC地址
                        if (TARGET_MAC.equalsIgnoreCase(mac)) {
                            String name = (String) param.getResult();
                            log("🎯 目标设备getName: " + name + " (" + mac + ")");
                            checkDevice(mac, name, -50); // 默认RSSI
                        }
                    }
                }
            });

            // Hook getAddress - 只处理目标MAC
            XposedHelpers.findAndHookMethod(deviceClass, "getAddress", new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    String mac = (String) param.getResult();

                    // 只处理目标MAC地址
                    if (TARGET_MAC.equalsIgnoreCase(mac)) {
                        // 避免短时间内重复日志
                        long currentTime = System.currentTimeMillis();
                        Long lastTime = lastLogTime.get(mac);
                        if (lastTime == null || (currentTime - lastTime) > 3000) { // 3秒内不重复日志
                            log("🎯 目标设备getAddress: " + mac);
                            lastLogTime.put(mac, currentTime);
                        }

                        // 总是检查目标设备
                        try {
                            BluetoothDevice device = (BluetoothDevice) param.thisObject;
                            String name = device.getName();
                            checkDevice(mac, name, -50); // 默认RSSI
                        } catch (Exception e) {
                            checkDevice(mac, "Unknown", -50);
                        }
                    }
                }
            });

            log("✅ BluetoothDevice多方法Hook已安装");
            success = true;

        } catch (Exception e) {
            log("❌ BluetoothDevice Hook失败: " + e.getMessage());
        }

        return success;
    }
    
    /**
     * 检查设备
     */
    private static void checkDevice(String mac, String name, int rssi) {
        try {
            boolean inRange = (rssi >= MIN_RSSI && rssi <= MAX_RSSI);
            boolean wasPresent = devicePresent.getOrDefault(mac, false);
            
            String timestamp = timeFormat.format(new Date());
            log("🔍 [" + timestamp + "] 检查设备: " + name + " (" + mac + ")");
            log("📶 RSSI: " + rssi + " dBm (范围: " + MIN_RSSI + " 到 " + MAX_RSSI + ")");
            log("✅ 在范围内: " + inRange + " | 之前存在: " + wasPresent);
            
            if (inRange && !wasPresent) {
                // 设备进入范围
                devicePresent.put(mac, true);
                log("🟢 ✅ 目标设备进入信号范围!");
                log("📍 设备: " + name + " (" + mac + ")");
                log("📶 信号强度: " + rssi + " dBm");
                executeCurl(CURL_APPEAR, "设备出现");
                
            } else if (!inRange && wasPresent) {
                // 设备离开范围
                devicePresent.put(mac, false);
                log("🔴 ❌ 目标设备离开信号范围!");
                log("📍 设备: " + name + " (" + mac + ")");
                log("📶 信号强度: " + rssi + " dBm");
                executeCurl(CURL_DISAPPEAR, "设备消失");
                
            } else if (inRange && wasPresent) {
                log("📍 目标设备持续在范围内: " + rssi + " dBm");
            } else {
                String reason = rssi < MIN_RSSI ? "信号太弱" : "信号太强";
                log("📶 目标设备不在范围: " + rssi + " dBm (" + reason + ")");
            }
            
        } catch (Exception e) {
            log("❌ 检查设备错误: " + e.getMessage());
        }
    }
    
    /**
     * 执行curl命令
     */
    private static void executeCurl(String command, String reason) {
        new Thread(() -> {
            try {
                log("🌐 执行curl (" + reason + "): " + command);
                
                Process process = Runtime.getRuntime().exec(new String[]{"sh", "-c", command});
                int exitCode = process.waitFor();
                
                if (exitCode == 0) {
                    log("✅ curl执行成功 (" + reason + ")");
                } else {
                    log("❌ curl执行失败 (" + reason + "), 退出码: " + exitCode);
                }
                
            } catch (Exception e) {
                log("❌ curl执行异常 (" + reason + "): " + e.getMessage());
            }
        }, "CurlExecutor").start();
    }
    
    /**
     * 启动监控线程
     */
    private static void startMonitor() {
        new Thread(() -> {
            try {
                Thread.sleep(10000); // 等待10秒
                log("🔍 监控线程启动 - 每30秒检查一次设备状态");

                while (true) {
                    try {
                        // 只显示目标设备状态
                        boolean targetPresent = devicePresent.getOrDefault(TARGET_MAC, false);
                        boolean targetDetected = lastLogTime.containsKey(TARGET_MAC);

                        if (targetDetected) {
                            log("🎯 目标设备 " + TARGET_MAC + " 状态: " + (targetPresent ? "✅ 在信号范围内" : "❌ 不在信号范围内"));
                        } else {
                            log("� 正在监控目标设备: " + TARGET_MAC + " (尚未检测到)");
                        }

                        Thread.sleep(30000); // 30秒检查一次
                    } catch (Exception e) {
                        log("❌ 监控线程错误: " + e.getMessage());
                        Thread.sleep(10000);
                    }
                }
            } catch (Exception e) {
                log("❌ 监控线程启动失败: " + e.getMessage());
            }
        }, "DeviceMonitor").start();
    }
    
    /**
     * 写入日志 - 简化版本
     */
    private static void log(String message) {
        String timestamp = timeFormat.format(new Date());
        String logLine = "[" + timestamp + "] " + message;

        // 输出到Xposed日志 (主要日志)
        XposedBridge.log(TAG + ": " + message);

        // 尝试写入简单日志文件 (可选，失败不影响功能)
        try {
            File simpleLog = new File(SIMPLE_LOG_FILE);
            try (FileWriter writer = new FileWriter(simpleLog, true)) {
                writer.write(logLine + "\n");
                writer.flush();
            }
        } catch (Exception e) {
            // 静默忽略文件写入错误，不影响Hook功能
        }
    }
}

R_DEF: Internal format may change without notice
local
color black
color purple_200
color purple_500
color purple_700
color teal_200
color teal_700
color white
drawable ic_launcher_background
drawable ic_launcher_foreground
id configButton
id configStatusText
id fabSettings
id moduleStatusText
id statusText
id testButton
id toolbar
layout activity_main
mipmap ic_launcher
mipmap ic_launcher_round
string app_description
string app_name
string bluetooth_not_enabled
string bluetooth_not_supported
string clear_log
string config_saved
string curl_command_absent
string curl_command_empty
string curl_command_present
string curl_executed
string curl_test_failed
string curl_test_success
string device_appeared
string device_disappeared
string device_not_found
string device_offline
string device_online
string enable_curl_on_disappear
string enable_module
string log_cleared
string max_rssi
string min_rssi
string module_disabled
string module_enabled
string permissions_denied
string permissions_granted
string permissions_required
string rssi_must_be_number
string scanning_started
string scanning_stopped
string signal_in_range
string signal_out_of_range
string signal_strength_range
string start_scanning
string status_bluetooth_unavailable
string status_module_disabled
string status_ready
string status_scanning
string stop_scanning
string target_mac_address
string test_curl
string version
style Theme.BluetoothScanner
style Theme.Lanya
xml backup_rules
xml data_extraction_rules

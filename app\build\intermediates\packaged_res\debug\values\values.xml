<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_description">基于RSSI的蓝牙设备距离检测模块</string>
    <string name="app_name">Lanya蓝牙距离检测</string>
    <string name="bluetooth_not_enabled">请先启用蓝牙</string>
    <string name="bluetooth_not_supported">设备不支持蓝牙</string>
    <string name="clear_log">清空日志</string>
    <string name="config_saved">配置已保存</string>
    <string name="curl_command_absent">设备消失时执行的curl命令</string>
    <string name="curl_command_empty">请输入curl命令</string>
    <string name="curl_command_present">设备出现时执行的curl命令</string>
    <string name="curl_executed">curl命令已执行</string>
    <string name="curl_test_failed">curl命令执行失败</string>
    <string name="curl_test_success">curl命令执行成功</string>
    <string name="device_appeared">设备出现</string>
    <string name="device_disappeared">设备消失</string>
    <string name="device_not_found">未发现</string>
    <string name="device_offline">离线</string>
    <string name="device_online">在线</string>
    <string name="enable_curl_on_disappear">设备消失时执行curl</string>
    <string name="enable_module">启用模块</string>
    <string name="log_cleared">日志已清空</string>
    <string name="max_rssi">最大值</string>
    <string name="min_rssi">最小值</string>
    <string name="module_disabled">模块禁用</string>
    <string name="module_enabled">模块启用</string>
    <string name="permissions_denied">部分权限被拒绝，功能可能受限</string>
    <string name="permissions_granted">所有权限已授予</string>
    <string name="permissions_required">需要蓝牙和位置权限才能正常工作</string>
    <string name="rssi_must_be_number">RSSI值必须是数字</string>
    <string name="scanning_started">开始蓝牙扫描</string>
    <string name="scanning_stopped">停止蓝牙扫描</string>
    <string name="signal_in_range">在范围内</string>
    <string name="signal_out_of_range">超出范围</string>
    <string name="signal_strength_range">信号强度范围 (dBm)</string>
    <string name="start_scanning">开始扫描</string>
    <string name="status_bluetooth_unavailable">蓝牙不可用</string>
    <string name="status_module_disabled">模块已禁用</string>
    <string name="status_ready">就绪</string>
    <string name="status_scanning">正在扫描...</string>
    <string name="stop_scanning">停止扫描</string>
    <string name="target_mac_address">目标MAC地址</string>
    <string name="test_curl">测试curl</string>
    <string name="version">v1.0.0-android15</string>
    <style name="Theme.BluetoothScanner" parent="Theme.Lanya"/>
    <style name="Theme.Lanya" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@android:color/holo_blue_dark</item>
        <item name="colorOnPrimary">@android:color/white</item>
        <item name="colorPrimaryContainer">@android:color/holo_blue_light</item>
        <item name="colorOnPrimaryContainer">@android:color/black</item>

        
        <item name="colorSecondary">@android:color/holo_green_dark</item>
        <item name="colorOnSecondary">@android:color/white</item>
        <item name="colorSecondaryContainer">@android:color/holo_green_light</item>
        <item name="colorOnSecondaryContainer">@android:color/black</item>

        
        <item name="colorSurface">@android:color/background_light</item>
        <item name="colorOnSurface">@android:color/black</item>
        <item name="colorSurfaceVariant">@android:color/background_dark</item>
        <item name="colorOnSurfaceVariant">@android:color/white</item>

        
        <item name="android:statusBarColor">@android:color/holo_blue_dark</item>
        <item name="android:navigationBarColor">@android:color/background_light</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">true</item>

        
        <item name="colorError">@android:color/holo_red_dark</item>
        <item name="colorOnError">@android:color/white</item>
        <item name="colorOutline">@android:color/darker_gray</item>
    </style>
</resources>
@echo off
echo ========================================
echo LSPosed 配置检查脚本
echo ========================================
echo.

echo 1. 检查LSPosed安装状态...
adb shell "pm list packages | grep lsposed"
if %errorlevel% equ 0 (
    echo ✅ LSPosed已安装
) else (
    echo ❌ LSPosed未安装
    echo 请先安装LSPosed框架
    pause
    exit /b 1
)
echo.

echo 2. 检查LSPosed服务状态...
adb shell "ps | grep lsposed"
echo.

echo 3. 检查模块APK安装状态...
adb shell "pm list packages | grep bluetoothscanner"
if %errorlevel% equ 0 (
    echo ✅ 蓝牙扫描器模块已安装
) else (
    echo ❌ 模块未安装
    echo 请先安装APK文件
)
echo.

echo 4. 检查Xposed模块配置...
echo 请手动在LSPosed管理器中确认：
echo.
echo 📱 模块列表中应该看到：
echo   - 蓝牙扫描器 (com.example.bluetoothscanner)
echo   - 状态：已启用 ✅
echo.
echo 🎯 作用域配置应该包含：
echo   - com.android.bluetooth ✅
echo   - system_server ✅  
echo   - com.example.bluetoothscanner ✅
echo.
echo 如果没有看到这些，请：
echo 1. 打开LSPosed管理器
echo 2. 找到"蓝牙扫描器"模块
echo 3. 点击启用开关
echo 4. 点击模块名称进入作用域设置
echo 5. 勾选上述三个进程
echo 6. 重启设备
echo.

pause

echo 5. 检查系统日志中的LSPosed信息...
echo LSPosed相关日志:
adb logcat -d | findstr -i "lsposed" | tail -10
echo.

echo 6. 检查Xposed模块加载日志...
echo 模块加载日志:
adb logcat -d | findstr -i "xposed.*load\|module.*load" | tail -10
echo.

echo 7. 检查蓝牙相关进程...
echo 蓝牙进程列表:
adb shell "ps | grep -E 'bluetooth|system_server'"
echo.

echo ========================================
echo 🔧 LSPosed配置指南：
echo.
echo 1. 打开LSPosed管理器
echo 2. 点击"模块"标签
echo 3. 找到"蓝牙扫描器"
echo 4. 确保开关为绿色（已启用）
echo 5. 点击模块名称
echo 6. 在"作用域"中勾选：
echo    - com.android.bluetooth
echo    - system_server
echo    - com.example.bluetoothscanner
echo 7. 返回并重启设备
echo 8. 重启后运行 debug-android15.bat 进行测试
echo ========================================
echo.

echo 按任意键退出...
pause >nul

#!/bin/bash

echo "正在构建蓝牙扫描器Xposed模块..."
echo

# 检查Gradle是否存在
if [ ! -f "gradlew" ]; then
    echo "错误：找不到gradlew文件"
    echo "请确保在项目根目录运行此脚本"
    exit 1
fi

# 给gradlew执行权限
chmod +x gradlew

# 清理项目
echo "清理项目..."
./gradlew clean
if [ $? -ne 0 ]; then
    echo "清理失败"
    exit 1
fi

# 构建APK
echo "构建APK..."
./gradlew assembleDebug
if [ $? -ne 0 ]; then
    echo "构建失败"
    exit 1
fi

echo
echo "构建成功！"
echo "APK文件位置：app/build/outputs/apk/debug/app-debug.apk"
echo
echo "安装说明："
echo "1. 将APK安装到已Root的Android设备"
echo "2. 在LSPosed管理器中启用此模块"
echo "3. 重启设备"
echo "4. 打开蓝牙扫描器应用进行配置"
echo

# LSPosed蓝牙距离检测模块开发计划

## 项目概述

本项目旨在开发一个LSPosed模块，通过Hook蓝牙功能实现基于RSSI（信号强度）的设备距离检测，并根据距离变化自动执行预设的网络请求。

## 功能特性

### 核心功能
- **蓝牙设备扫描**: 实时扫描周围蓝牙设备
- **距离检测**: 基于RSSI值判断与目标设备的距离变化
- **自动化执行**: 根据距离变化自动执行预设的curl命令
- **后台运行**: 无需打开APP即可在后台持续工作

### 配置功能
- **设备识别**: 支持通过MAC地址或蓝牙名称识别目标设备
- **距离阈值**: 可配置接近和远离的RSSI阈值
- **网络请求**: 可配置接近和远离时执行的curl命令
- **开关控制**: 可启用/禁用监控功能
- **蓝牙管理**: 支持停止蓝牙进程，无需重启手机即可使模块生效

## 技术架构

### 模块组成
1. **LSPosed Hook模块** - 核心功能实现
2. **配置APP** - 用户界面和设置管理
3. **数据存储** - 配置信息持久化

### Hook目标
**系统框架级Hook (推荐)**：
- `BluetoothAdapter` - 系统蓝牙适配器核心方法
  - `startDiscovery()` - 经典蓝牙扫描
  - `getRemoteDevice()` - 设备对象获取
- `BluetoothLeScanner` - BLE扫描器系统服务
  - `startScan()` - BLE扫描启动
  - `onScanResult()` - 扫描结果回调
- `BluetoothManager` - 蓝牙管理器系统服务
  - `getAdapter()` - 适配器获取
- `BluetoothDevice` - 设备对象系统类
  - `getName()` - 设备名称获取
  - `getAddress()` - MAC地址获取

**为什么选择系统框架**：
1. **全局拦截**: 能拦截所有应用和系统服务的蓝牙调用
2. **底层访问**: 直接Hook Android系统的蓝牙框架代码
3. **稳定可靠**: 不依赖特定应用进程，系统级服务更稳定
4. **兼容性好**: 适用于所有厂商ROM和Android版本

## 开发计划

### 阶段一：项目初始化 (1-2天)
- [ ] 创建Android项目结构
- [ ] 配置Gradle构建脚本
- [ ] 集成LSPosed框架依赖
- [ ] 设置开发环境

### 阶段二：Hook模块开发 (3-5天)
- [ ] 实现蓝牙扫描Hook
- [ ] 开发RSSI监控逻辑
- [ ] 实现距离变化检测算法
- [ ] 添加网络请求执行功能
- [ ] 实现配置数据读取

### 阶段三：配置APP开发 (2-3天)
- [ ] 设计用户界面
- [ ] 实现设备选择功能
- [ ] 添加curl命令配置
- [ ] 实现配置数据存储
- [ ] 添加功能开关控制
- [ ] 实现蓝牙进程管理功能

### 阶段四：集成测试 (1-2天)
- [ ] 模块功能测试
- [ ] APP配置测试
- [ ] 端到端集成测试
- [ ] 性能优化

### 阶段五：打包发布 (1天)
- [ ] 代码优化和清理
- [ ] 生成签名APK
- [ ] 编写使用文档

## 技术要求

### 开发环境
- **JDK**: OpenJDK 17+ (Android 15兼容性要求)
- **构建工具**: Gradle 8.0+
- **Android SDK**: API Level 21+ (Android 5.0+)
- **目标SDK**: API Level 35 (Android 15)
- **编译SDK**: API Level 35 (Android 15)
- **LSPosed**: v1.9.2+ (支持Android 15)

### 权限需求
```xml
<!-- Android 15兼容权限配置 -->
<!-- 传统蓝牙权限 (API < 31) -->
<uses-permission android:name="android.permission.BLUETOOTH"
    android:maxSdkVersion="30" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN"
    android:maxSdkVersion="30" />

<!-- 新蓝牙权限 (API >= 31, Android 12+) -->
<uses-permission android:name="android.permission.BLUETOOTH_SCAN"
    android:usesPermissionFlags="neverForLocation" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
<uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />

<!-- 位置权限 -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

<!-- 网络权限 -->
<uses-permission android:name="android.permission.INTERNET" />

<!-- 进程管理权限 -->
<uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />

<!-- Android 15特定权限 -->
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

<!-- 硬件特性声明 -->
<uses-feature android:name="android.hardware.bluetooth_le" android:required="true" />
<uses-feature android:name="android.hardware.bluetooth" android:required="true" />
```

### 依赖库
- **LSPosed API**: v1.9.2+ (Android 15兼容)
- **OkHttp**: 4.12.0+ (网络请求)
- **SharedPreferences**: 原生API (配置存储)
- **Material Design Components**: 1.11.0+ (Android 15适配)
- **AndroidX Core**: 1.12.0+ (兼容性支持)
- **AndroidX Activity**: 1.8.0+ (权限请求)
- **AndroidX Fragment**: 1.6.0+ (界面管理)

## 项目结构
```
lanya/
├── app/                          # 配置APP
│   ├── src/main/
│   │   ├── java/com/lanya/config/
│   │   │   ├── MainActivity.java
│   │   │   ├── ConfigActivity.java
│   │   │   ├── SettingsManager.java
│   │   │   ├── BluetoothManager.java
│   │   │   ├── PermissionManager.java    # Android 15权限管理
│   │   │   └── ForegroundService.java    # 前台服务
│   │   ├── res/
│   │   │   ├── xml/
│   │   │   │   └── network_security_config.xml
│   │   │   └── values/
│   │   │       └── strings.xml
│   │   └── AndroidManifest.xml
│   └── build.gradle
├── hook/                         # LSPosed Hook模块
│   ├── src/main/
│   │   ├── java/com/lanya/hook/
│   │   │   ├── BluetoothHook.java
│   │   │   ├── DistanceDetector.java
│   │   │   ├── NetworkExecutor.java
│   │   │   ├── Android15Compatibility.java  # Android 15兼容性处理
│   │   │   └── PermissionHelper.java        # 权限辅助类
│   │   ├── assets/
│   │   │   └── xposed_init                  # LSPosed入口配置
│   │   └── AndroidManifest.xml
│   └── build.gradle
├── shared/                       # 共享代码
│   └── src/main/java/com/lanya/shared/
│       ├── Config.java
│       └── Constants.java
├── build.gradle
├── settings.gradle
└── README.md
```

## 配置说明

### 设备识别配置
- **识别方式**: MAC地址 或 蓝牙名称
- **MAC地址格式**: XX:XX:XX:XX:XX:XX
- **名称匹配**: 支持完全匹配和模糊匹配

### 距离阈值配置
- **接近阈值**: 例如 -90dBm → -85dBm → -80dBm
- **远离阈值**: 例如 -40dBm → -50dBm → -60dBm
- **检测间隔**: 扫描频率设置

### 网络请求配置
- **接近时执行**: curl命令或HTTP请求
- **远离时执行**: curl命令或HTTP请求
- **请求超时**: 网络请求超时设置

### 蓝牙进程管理
- **进程停止**: 通过APP停止蓝牙相关进程
- **配置重载**: 无需重启手机即可重新加载模块配置
- **状态监控**: 实时显示蓝牙服务状态

## 使用流程

1. **安装模块**: 将编译好的APK安装到设备
2. **激活模块**: 在LSPosed管理器中激活模块
3. **重启系统**: 重启手机使LSPosed模块生效
4. **配置设置**: 打开配置APP设置目标设备和命令
5. **启用监控**: 开启蓝牙距离监控功能
6. **停止蓝牙进程**: 如需重新加载配置，可使用APP内的蓝牙进程停止功能
7. **后台运行**: 模块将在后台持续工作

## 注意事项

### 技术限制
- 需要Root权限和LSPosed框架
- 蓝牙必须保持开启状态
- RSSI值受环境因素影响较大

### Android 15特定注意事项
- **权限管理**: Android 15加强了蓝牙权限管理，需要动态请求权限
- **后台限制**: 需要使用前台服务来保证后台扫描功能
- **电池优化**: 需要引导用户关闭电池优化以确保持续运行
- **通知权限**: Android 15需要显式请求通知权限
- **LSPosed兼容**: 确保使用支持Android 15的LSPosed版本

### LSPosed作用域重要提醒
⚠️ **关键配置**：
- **必须选择"系统框架"**: 这是模块正常工作的前提
- **不要选择蓝牙应用**: 会导致Hook范围受限，功能异常
- **重启后检查**: 确认模块在系统框架中保持激活状态
- **日志监控**: 通过LSPosed日志确认Hook是否成功

### 隐私安全
- 仅扫描不连接设备
- 配置信息本地存储
- 网络请求需用户授权

### 性能考虑
- 合理设置扫描间隔避免耗电
- 异步执行网络请求
- 优化内存使用
- **Android 15优化**: 使用JobScheduler优化后台任务
- **电池管理**: 实现智能扫描频率调节
- **内存管理**: 遵循Android 15的内存管理最佳实践

## LSPosed作用域选择详解

### 系统框架 vs 蓝牙应用对比

| 特性 | 系统框架 (android) | 蓝牙应用 (com.android.bluetooth) |
|------|-------------------|----------------------------------|
| **Hook范围** | 全系统蓝牙API调用 | 仅蓝牙应用进程内 |
| **稳定性** | ⭐⭐⭐⭐⭐ 系统级服务 | ⭐⭐⭐ 应用进程可能重启 |
| **兼容性** | ⭐⭐⭐⭐⭐ 所有ROM通用 | ⭐⭐ 部分ROM无独立蓝牙应用 |
| **权限级别** | 系统级权限 | 应用级权限 |
| **Hook时机** | 系统启动时 | 蓝牙应用启动时 |
| **覆盖范围** | 所有蓝牙调用 | 仅应用内调用 |

### 技术原理分析

#### 系统框架Hook原理
```java
// Hook系统框架中的BluetoothAdapter
public class BluetoothHook implements IXposedHookLoadPackage {
    @Override
    public void handleLoadPackage(XC_LoadPackage.LoadPackageParam lpparam) {
        if (lpparam.packageName.equals("android")) {
            // Hook系统框架中的蓝牙服务
            XposedHelpers.findAndHookMethod(
                "android.bluetooth.BluetoothAdapter",
                lpparam.classLoader,
                "startDiscovery",
                new XC_MethodHook() {
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) {
                        // 拦截所有应用的蓝牙扫描请求
                        handleBluetoothScan();
                    }
                }
            );
        }
    }
}
```

#### 为什么不推荐蓝牙应用Hook

1. **进程隔离问题**
   - 蓝牙应用运行在独立进程中
   - 其他应用直接调用系统API时无法拦截
   - 进程被杀死后Hook失效

2. **厂商定制化**
   - 小米、华为等厂商可能没有独立的蓝牙应用
   - 蓝牙功能集成在系统服务中
   - 包名和架构可能不同

3. **Android版本差异**
   - Android 15中蓝牙架构有所调整
   - 某些功能可能迁移到系统服务
   - 应用级Hook可能失效

### 最佳实践建议

✅ **推荐配置**：
```
作用域: 系统框架 (android)
Hook目标: BluetoothAdapter, BluetoothLeScanner
运行模式: 系统级服务Hook
```

❌ **避免配置**：
```
作用域: 蓝牙应用 (com.android.bluetooth)
原因: 覆盖范围有限，稳定性差
```

## 距离检测算法详解

### RSSI值与距离关系
- **RSSI范围**: 通常在-30dBm到-100dBm之间
- **距离估算**: RSSI值越高（越接近0）表示距离越近
- **环境影响**: 障碍物、干扰源会影响RSSI准确性

### 检测逻辑
1. **数据采集**: 持续收集目标设备的RSSI值
2. **趋势分析**: 分析最近N次扫描的RSSI变化趋势
3. **阈值判断**: 根据配置的阈值判断接近或远离
4. **防抖处理**: 避免因信号波动导致的误触发

### 示例配置
```
接近检测: -90dBm → -85dBm → -80dBm (信号增强，距离减小)
远离检测: -40dBm → -50dBm → -60dBm (信号减弱，距离增大)
```

## 后续扩展

- 支持多设备同时监控
- 添加距离变化趋势分析
- 实现更复杂的触发条件
- 支持自定义脚本执行
- 添加日志记录功能
- 支持WiFi信号强度检测
- 添加地理围栏功能

## 快速开始

### 开发环境准备
```bash
# 1. 安装OpenJDK 17+ (Android 15要求)
# Windows: 下载并安装 OpenJDK 17
# Linux: sudo apt install openjdk-17-jdk
# macOS: brew install openjdk@17

# 2. 下载Android SDK (包含API 35)
# 通过Android Studio或命令行工具安装

# 3. 设置环境变量
export JAVA_HOME=/path/to/openjdk-17
export ANDROID_HOME=/path/to/android-sdk

# 4. 克隆项目
git clone <repository-url>
cd lanya

# 5. 检查Gradle版本 (需要8.0+)
./gradlew --version

# 6. 构建项目 (Android 15兼容)
./gradlew clean build

# 7. 生成APK
./gradlew assembleRelease
```

### 部署步骤 (Android 15)
1. **确保设备已Root并安装LSPosed框架**
   - 推荐使用LSPosed v1.9.2+版本
   - 确认LSPosed支持Android 15

2. **安装生成的APK文件**
   ```bash
   adb install -r lanya-release.apk
   ```

3. **在LSPosed管理器中激活模块**
   - 打开LSPosed管理器
   - 找到"Lanya蓝牙距离检测"模块
   - 勾选激活并选择作用域

   **作用域选择说明**：

   🔧 **推荐：系统框架 (android)**
   - ✅ Hook系统级蓝牙服务，覆盖范围最广
   - ✅ 能拦截所有应用的蓝牙扫描操作
   - ✅ 访问底层BluetoothAdapter和BluetoothManager
   - ✅ 稳定性高，不受应用进程重启影响
   - ✅ 能Hook系统蓝牙服务的核心方法

   ❌ **不推荐：蓝牙应用 (com.android.bluetooth)**
   - ❌ 只能Hook蓝牙应用进程，覆盖有限
   - ❌ 蓝牙应用可能被系统杀死或重启
   - ❌ 某些厂商ROM可能没有独立的蓝牙应用
   - ❌ 无法拦截其他应用直接调用系统API的情况
   - ❌ Android 15中蓝牙架构变化可能影响Hook效果

4. **重启设备**
   ```bash
   adb reboot
   ```

5. **配置权限 (Android 15重要)**
   - 打开配置APP
   - 授予所有蓝牙相关权限
   - 授予位置权限
   - 授予通知权限
   - 关闭电池优化

6. **进行功能设置**

### 配置示例 (Android 15优化)
```
目标设备: 选择MAC地址 "AA:BB:CC:DD:EE:FF"
接近阈值: -90, -85, -80 (dBm)
远离阈值: -40, -50, -60 (dBm)
扫描间隔: 5秒 (Android 15电池优化)
接近命令: curl -X POST "http://example.com/api/approach" -H "Content-Type: application/json"
远离命令: curl -X POST "http://example.com/api/leave" -H "Content-Type: application/json"
前台服务: 启用 (Android 15后台限制)
通知显示: 启用 (显示扫描状态)
```

### Android 15特定配置
```xml
<!-- build.gradle (app) -->
android {
    compileSdk 35
    targetSdk 35
    minSdk 21

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
}

<!-- AndroidManifest.xml -->
<application
    android:requestLegacyExternalStorage="false"
    android:allowBackup="false"
    android:networkSecurityConfig="@xml/network_security_config">

    <service android:name=".ForegroundService"
        android:foregroundServiceType="location"
        android:exported="false" />
</application>
```

## Android 15测试清单

### 功能测试
- [ ] 蓝牙权限动态请求测试
- [ ] 前台服务正常运行测试
- [ ] 后台扫描功能测试
- [ ] 电池优化白名单测试
- [ ] 通知权限和显示测试
- [ ] LSPosed模块激活测试

### 兼容性测试
- [ ] Android 15设备兼容性
- [ ] 不同厂商ROM适配
- [ ] 权限拒绝场景处理
- [ ] 系统升级后功能保持

---

**开发者**: Lanya Team
**版本**: v1.0.0 (Android 15兼容版)
**目标平台**: Android 5.0+ (API 21+)
**优化平台**: Android 15 (API 35)
**更新时间**: 2025-07-28

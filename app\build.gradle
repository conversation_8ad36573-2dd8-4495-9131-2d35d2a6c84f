plugins {
    id 'com.android.application'
}

android {
    namespace 'com.lanya.bluetoothscanner'
    compileSdk 35

    defaultConfig {
        applicationId "com.lanya.bluetoothscanner"
        minSdk 21
        targetSdk 35
        versionCode 1
        versionName "1.0.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.debug
        }
        debug {
            minifyEnabled false
            debuggable true
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    packagingOptions {
        resources {
            excludes += ['META-INF/DEPENDENCIES', 'META-INF/LICENSE', 'META-INF/LICENSE.txt', 'META-INF/NOTICE', 'META-INF/NOTICE.txt']
        }
    }
}

dependencies {
    // AndroidX Core (Android 15兼容)
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.core:core:1.12.0'
    implementation 'androidx.activity:activity:1.8.0'
    implementation 'androidx.fragment:fragment:1.6.0'

    // Material Design Components (Android 15适配)
    implementation 'com.google.android.material:material:1.11.0'

    // 网络请求
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'

    // LSPosed API (Android 15兼容)
    compileOnly 'de.robv.android.xposed:api:82'
    compileOnly 'de.robv.android.xposed:api:82:sources'

    // 测试依赖
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}

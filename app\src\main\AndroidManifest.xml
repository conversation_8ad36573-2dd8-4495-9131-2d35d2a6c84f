<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Android 15兼容权限配置 -->
    <!-- 传统蓝牙权限 (API < 31) -->
    <uses-permission android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" />

    <!-- 新蓝牙权限 (API >= 31, Android 12+) -->
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />

    <!-- 位置权限 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />

    <!-- 进程管理权限 -->
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />

    <!-- Android 15特定权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- 存储权限 (兼容性) -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />

    <!-- 硬件特性声明 -->
    <uses-feature android:name="android.hardware.bluetooth_le" android:required="true" />
    <uses-feature android:name="android.hardware.bluetooth" android:required="true" />

    <application
        android:allowBackup="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.BluetoothScanner"
        android:requestLegacyExternalStorage="false"
        android:enableOnBackInvokedCallback="true"
        android:networkSecurityConfig="@xml/network_security_config">

        <!-- 主界面 - 显示配置和日志 -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="Lanya蓝牙距离检测"
            android:theme="@style/Theme.BluetoothScanner">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 前台服务 (Android 15要求) -->
        <service android:name=".ForegroundService"
            android:foregroundServiceType="location"
            android:exported="false" />

        <!-- LSPosed 模块元数据 -->
        <meta-data
            android:name="xposedmodule"
            android:value="true" />
        <meta-data
            android:name="xposeddescription"
            android:value="LSPosed蓝牙距离检测模块 - 基于RSSI信号强度自动执行网络请求" />
        <meta-data
            android:name="xposedminversion"
            android:value="93" />

    </application>

</manifest>

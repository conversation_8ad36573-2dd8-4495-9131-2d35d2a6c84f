-- Merging decision tree log ---
manifest
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:2:1-70:12
INJECTED from C:\xposedshell\app\src\main\AndroidManifest.xml:2:1-70:12
INJECTED from C:\xposedshell\app\src\main\AndroidManifest.xml:2:1-70:12
INJECTED from C:\xposedshell\app\src\main\AndroidManifest.xml:2:1-70:12
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c486cec99d4fccb7893779ad5d22534\transformed\material-1.9.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\0048526f799978d53c8838b69626f826\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\902bda61c99a6c8f4092b3028f6c7343\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3c82f0ccdbe9412677623492bdd8999d\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\17473142177f1aaac34c6335ae7e71fa\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\560ccd516cd6de1b6638828af3eebb71\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\37c02d9dbf743df3639b5dd94d23bea1\transformed\activity-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\96e99f535981952eca70dcea7436f9a8\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e38ee089648600abdf2b1b34f81c65de\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\41f5bc421223c690c51c8e734819e8da\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\61bcdcb940fbb590286c186b857db46a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\61017ffc1d01ff413e7d898b997b69c2\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2deddba07ed6cbf9631edbc4999ddc1a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c163b3c0f223d55e9867cb5b985a609\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\07dd9cf1e5e9d3a0834cbd1953c0cf54\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f0cd2140268ad88a6efad3ac05386ab8\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\205605b76180136cc8f42c99fdbd78ad\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\dd7cfcb5a58d5491bfcb53900100245d\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\e80bc102ae7b3ad78be9ca567c1ef740\transformed\core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b0dec12bf907a0ce71737e1bcb2c2d1\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\74257452692e5c2c34a27252223c312c\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\711082ff04b262488c989f0a20113488\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0fd9d8751b56a2d26f0d29d3b3e9f257\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\8af27606e6293b3b18c49e9b214e0d7e\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\f0e7d6d799d6dcb9d78b59a41c74ca7d\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\baa876de43fb379b2b6dab8fbed6b608\transformed\savedstate-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf7e18ef8301d1d7f6bc63773afcfb9c\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b905560b2da4be86f30e02660b710b15\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b2183c3cbd89865b4ee0e8a658ff57b\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b2a96e02639f78061d3b89549351b289\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a642dd31dddf0535e9c303cc38f8bf5\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\603b40d55b283007ec925a6b3494c797\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ee6b80c7f1b747b930e882ed6e406f7\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\14cf0f12229a19f9854ef132476f78e1\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9b79652572637781f542df77a91d7cc\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\43551e7787504844afc94f958065b3d6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0ef88f649878603ab3d2e983b9bc7b1\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c15864de81153c1f158ef9d18de9ad3a\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
INJECTED from C:\xposedshell\app\src\main\AndroidManifest.xml:2:1-70:12
INJECTED from C:\xposedshell\app\src\main\AndroidManifest.xml:2:1-70:12
INJECTED from C:\xposedshell\app\src\main\AndroidManifest.xml:2:1-70:12
	package
		INJECTED from C:\xposedshell\app\src\main\AndroidManifest.xml
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:2:1-70:12
		INJECTED from C:\xposedshell\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\xposedshell\app\src\main\AndroidManifest.xml
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:2:1-70:12
		INJECTED from C:\xposedshell\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\xposedshell\app\src\main\AndroidManifest.xml
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:2:1-70:12
		INJECTED from C:\xposedshell\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.BLUETOOTH
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:7:5-8:38
	android:maxSdkVersion
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:8:9-35
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:7:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:9:5-10:38
	android:maxSdkVersion
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:10:9-35
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:9:22-71
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:13:5-14:58
	android:usesPermissionFlags
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:14:9-55
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:13:22-70
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:15:5-76
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:15:22-73
uses-permission#android.permission.BLUETOOTH_ADVERTISE
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:16:5-78
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:16:22-75
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:19:5-79
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:19:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:20:5-81
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:20:22-78
uses-permission#android.permission.INTERNET
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:23:5-67
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:23:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:24:5-79
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:24:22-76
uses-permission#android.permission.KILL_BACKGROUND_PROCESSES
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:27:5-84
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:27:22-81
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:30:5-77
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:30:22-74
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:31:5-77
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:31:22-74
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:32:5-68
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:32:22-65
uses-feature#android.hardware.bluetooth_le
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:35:5-90
	android:required
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:35:64-87
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:35:19-63
uses-feature#android.hardware.bluetooth
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:36:5-87
	android:required
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:36:61-84
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:36:19-60
uses-feature#android.hardware.location
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:37:5-87
	android:required
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:37:60-84
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:37:19-59
application
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:39:5-68:19
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c486cec99d4fccb7893779ad5d22534\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c486cec99d4fccb7893779ad5d22534\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\0048526f799978d53c8838b69626f826\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\0048526f799978d53c8838b69626f826\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\8af27606e6293b3b18c49e9b214e0d7e\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\8af27606e6293b3b18c49e9b214e0d7e\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b905560b2da4be86f30e02660b710b15\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b905560b2da4be86f30e02660b710b15\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b2a96e02639f78061d3b89549351b289\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b2a96e02639f78061d3b89549351b289\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:44:9-35
	android:label
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:42:9-41
	android:roundIcon
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:43:9-54
	tools:targetApi
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:46:9-29
	android:icon
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:41:9-43
	android:allowBackup
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:40:9-35
	android:theme
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:45:9-54
activity#com.example.bluetoothscanner.MainActivity
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:49:9-56:20
	android:exported
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:51:13-36
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:50:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:52:13-55:29
action#android.intent.action.MAIN
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:53:17-69
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:53:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:54:17-77
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:54:27-74
meta-data#xposedmodule
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:59:9-61:36
	android:value
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:61:13-33
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:60:13-40
meta-data#xposeddescription
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:62:9-64:48
	android:value
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:64:13-45
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:63:13-45
meta-data#xposedminversion
ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:65:9-67:34
	android:value
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:67:13-31
	android:name
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml:66:13-44
uses-sdk
INJECTED from C:\xposedshell\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\xposedshell\app\src\main\AndroidManifest.xml
INJECTED from C:\xposedshell\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c486cec99d4fccb7893779ad5d22534\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c486cec99d4fccb7893779ad5d22534\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\0048526f799978d53c8838b69626f826\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\0048526f799978d53c8838b69626f826\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\902bda61c99a6c8f4092b3028f6c7343\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\902bda61c99a6c8f4092b3028f6c7343\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3c82f0ccdbe9412677623492bdd8999d\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3c82f0ccdbe9412677623492bdd8999d\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\17473142177f1aaac34c6335ae7e71fa\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\17473142177f1aaac34c6335ae7e71fa\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\560ccd516cd6de1b6638828af3eebb71\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\560ccd516cd6de1b6638828af3eebb71\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\37c02d9dbf743df3639b5dd94d23bea1\transformed\activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\37c02d9dbf743df3639b5dd94d23bea1\transformed\activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\96e99f535981952eca70dcea7436f9a8\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\96e99f535981952eca70dcea7436f9a8\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e38ee089648600abdf2b1b34f81c65de\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e38ee089648600abdf2b1b34f81c65de\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\41f5bc421223c690c51c8e734819e8da\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\41f5bc421223c690c51c8e734819e8da\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\61bcdcb940fbb590286c186b857db46a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\61bcdcb940fbb590286c186b857db46a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\61017ffc1d01ff413e7d898b997b69c2\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\61017ffc1d01ff413e7d898b997b69c2\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2deddba07ed6cbf9631edbc4999ddc1a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2deddba07ed6cbf9631edbc4999ddc1a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c163b3c0f223d55e9867cb5b985a609\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c163b3c0f223d55e9867cb5b985a609\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\07dd9cf1e5e9d3a0834cbd1953c0cf54\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\07dd9cf1e5e9d3a0834cbd1953c0cf54\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f0cd2140268ad88a6efad3ac05386ab8\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f0cd2140268ad88a6efad3ac05386ab8\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\205605b76180136cc8f42c99fdbd78ad\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\205605b76180136cc8f42c99fdbd78ad\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\dd7cfcb5a58d5491bfcb53900100245d\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\dd7cfcb5a58d5491bfcb53900100245d\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\e80bc102ae7b3ad78be9ca567c1ef740\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\e80bc102ae7b3ad78be9ca567c1ef740\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b0dec12bf907a0ce71737e1bcb2c2d1\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b0dec12bf907a0ce71737e1bcb2c2d1\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\74257452692e5c2c34a27252223c312c\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\74257452692e5c2c34a27252223c312c\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\711082ff04b262488c989f0a20113488\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\711082ff04b262488c989f0a20113488\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0fd9d8751b56a2d26f0d29d3b3e9f257\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0fd9d8751b56a2d26f0d29d3b3e9f257\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\8af27606e6293b3b18c49e9b214e0d7e\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\8af27606e6293b3b18c49e9b214e0d7e\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\f0e7d6d799d6dcb9d78b59a41c74ca7d\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\f0e7d6d799d6dcb9d78b59a41c74ca7d\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\baa876de43fb379b2b6dab8fbed6b608\transformed\savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\baa876de43fb379b2b6dab8fbed6b608\transformed\savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf7e18ef8301d1d7f6bc63773afcfb9c\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf7e18ef8301d1d7f6bc63773afcfb9c\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b905560b2da4be86f30e02660b710b15\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b905560b2da4be86f30e02660b710b15\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b2183c3cbd89865b4ee0e8a658ff57b\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b2183c3cbd89865b4ee0e8a658ff57b\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b2a96e02639f78061d3b89549351b289\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b2a96e02639f78061d3b89549351b289\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a642dd31dddf0535e9c303cc38f8bf5\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a642dd31dddf0535e9c303cc38f8bf5\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\603b40d55b283007ec925a6b3494c797\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\603b40d55b283007ec925a6b3494c797\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ee6b80c7f1b747b930e882ed6e406f7\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ee6b80c7f1b747b930e882ed6e406f7\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\14cf0f12229a19f9854ef132476f78e1\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\14cf0f12229a19f9854ef132476f78e1\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9b79652572637781f542df77a91d7cc\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9b79652572637781f542df77a91d7cc\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\43551e7787504844afc94f958065b3d6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\43551e7787504844afc94f958065b3d6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0ef88f649878603ab3d2e983b9bc7b1\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0ef88f649878603ab3d2e983b9bc7b1\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c15864de81153c1f158ef9d18de9ad3a\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c15864de81153c1f158ef9d18de9ad3a\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
INJECTED from C:\xposedshell\app\src\main\AndroidManifest.xml
INJECTED from C:\xposedshell\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\xposedshell\app\src\main\AndroidManifest.xml
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml
		INJECTED from C:\xposedshell\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\xposedshell\app\src\main\AndroidManifest.xml
		ADDED from C:\xposedshell\app\src\main\AndroidManifest.xml
		INJECTED from C:\xposedshell\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\8af27606e6293b3b18c49e9b214e0d7e\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\8af27606e6293b3b18c49e9b214e0d7e\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b905560b2da4be86f30e02660b710b15\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b905560b2da4be86f30e02660b710b15\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.example.bluetoothscanner.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.bluetoothscanner.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\43eeba27e46c0f9e8270c8e6a59600bf\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\8af27606e6293b3b18c49e9b214e0d7e\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\8af27606e6293b3b18c49e9b214e0d7e\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\8af27606e6293b3b18c49e9b214e0d7e\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78

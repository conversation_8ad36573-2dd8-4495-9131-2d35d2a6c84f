package com.example.bluetoothscanner;

import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage;

/**
 * 极简LSPosed模块 - 专注核心功能
 */
public class XposedModule implements IXposedHookLoadPackage {

    private static final String TAG = "BluetoothHook";

    @Override
    public void handleLoadPackage(XC_LoadPackage.LoadPackageParam lpparam) throws Throwable {

        // 记录所有进程加载信息（用于调试）
        XposedBridge.log(TAG + ": 📦 进程加载: " + lpparam.packageName + " (PID: " + android.os.Process.myPid() + ")");

        // 只Hook蓝牙进程，避免影响系统稳定性
        if (lpparam.packageName.equals("com.android.bluetooth")) {

            XposedBridge.log(TAG + ": 🎯 开始Hook蓝牙进程: " + lpparam.packageName);
            XposedBridge.log(TAG + ": 🔧 进程ID: " + android.os.Process.myPid());
            XposedBridge.log(TAG + ": 📱 Android版本: " + android.os.Build.VERSION.SDK_INT);

            try {
                // 使用安全的Hook方法
                SimpleBluetoothHook.init(lpparam.classLoader, lpparam.packageName);
                XposedBridge.log(TAG + ": ✅ 蓝牙Hook初始化成功");

            } catch (Exception e) {
                XposedBridge.log(TAG + ": ❌ 蓝牙Hook失败: " + e.getMessage());
                XposedBridge.log(TAG + ": 📋 错误堆栈: " + android.util.Log.getStackTraceString(e));
            }
        }

        // 也Hook自己的应用以实现自检
        if (lpparam.packageName.equals("com.example.bluetoothscanner")) {
            XposedBridge.log(TAG + ": 🔍 Hook自身应用进行自检");
            try {
                // Hook MainActivity的isModuleActive方法
                Class<?> mainActivityClass = XposedHelpers.findClass("com.example.bluetoothscanner.MainActivity", lpparam.classLoader);
                XposedHelpers.findAndHookMethod(mainActivityClass, "isModuleActive", new XC_MethodHook() {
                    @Override
                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                        param.setResult(true);
                        XposedBridge.log(TAG + ": ✅ 自检Hook触发 - 模块已激活!");
                    }
                });
                XposedBridge.log(TAG + ": ✅ 自检Hook安装成功");
            } catch (Exception e) {
                XposedBridge.log(TAG + ": ❌ 自检Hook失败: " + e.getMessage());
            }
        }
    }
}

package com.lanya.bluetoothscanner;

import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage;

/**
 * 极简LSPosed模块 - 专注核心功能
 */
public class XposedModule implements IXposedHookLoadPackage {

    private static final String TAG = "BluetoothHook";

    @Override
    public void handleLoadPackage(XC_LoadPackage.LoadPackageParam lpparam) throws Throwable {

        // 记录所有进程加载信息（用于调试）
        XposedBridge.log(TAG + ": 📦 进程加载: " + lpparam.packageName + " (PID: " + android.os.Process.myPid() + ")");

        // Hook系统框架 - 推荐做法，覆盖范围最广
        if (lpparam.packageName.equals("android")) {

            XposedBridge.log(TAG + ": 🎯 开始Hook系统框架: " + lpparam.packageName);
            XposedBridge.log(TAG + ": 🔧 进程ID: " + android.os.Process.myPid());
            XposedBridge.log(TAG + ": 📱 Android版本: " + android.os.Build.VERSION.SDK_INT);
            XposedBridge.log(TAG + ": ⚠️ 重要: 使用系统框架Hook，能拦截所有应用的蓝牙调用");

            try {
                // 使用安全的Hook方法
                SimpleBluetoothHook.init(lpparam.classLoader, lpparam.packageName);
                XposedBridge.log(TAG + ": ✅ 系统框架蓝牙Hook初始化成功");

            } catch (Exception e) {
                XposedBridge.log(TAG + ": ❌ 系统框架蓝牙Hook失败: " + e.getMessage());
                XposedBridge.log(TAG + ": 📋 错误堆栈: " + android.util.Log.getStackTraceString(e));
            }
        }

        // 也Hook自己的应用以实现自检
        if (lpparam.packageName.equals("com.lanya.bluetoothscanner")) {
            XposedBridge.log(TAG + ": 🔍 Hook自身应用进行自检");
            try {
                // Hook MainActivity的isModuleActive方法
                Class<?> mainActivityClass = XposedHelpers.findClass("com.lanya.bluetoothscanner.MainActivity", lpparam.classLoader);
                XposedHelpers.findAndHookMethod(mainActivityClass, "isModuleActive", new XC_MethodHook() {
                    @Override
                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                        param.setResult(true);
                        XposedBridge.log(TAG + ": ✅ 自检Hook触发 - 模块已激活!");
                    }
                });
                XposedBridge.log(TAG + ": ✅ 自检Hook安装成功");
            } catch (Exception e) {
                XposedBridge.log(TAG + ": ❌ 自检Hook失败: " + e.getMessage());
            }
        }
    }
}
